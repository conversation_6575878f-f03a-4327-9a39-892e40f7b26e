package de.interzero.oneepr.customer.certificate;

import de.interzero.oneepr.customer.certificate.dto.CreateCertificateDto;
import de.interzero.oneepr.customer.certificate.dto.CreateCertificatePdfDto;
import de.interzero.oneepr.customer.certificate.dto.UpdateCertificateDto;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class CertificateService {

    private final CertificateRepository certificateRepository;

    private final LicenseRepository licenseRepository;

    private final CertificatePdfService pdfService;

    private static final String CERTIFICATE_NOT_FOUND = "Certificate not found";

    private static final String CERTIFICATE_ID_IS_INVALID = "Certificate ID is invalid";

    /**
     * Retrieves all certificates associated with the given license ID,
     * excluding certificates marked as deleted.
     *
     * @param licenseId the ID of the license whose certificates are to be retrieved;
     *                  must not be null.
     * @return a list of certificates associated with the provided license ID
     * that have not been marked as deleted.
     * @throws ResponseStatusException if the license ID is null or invalid.
     */
    @Transactional
    public List<Certificate> findAll(Integer licenseId) {
        if (licenseId == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License ID is invalid");
        }

        return certificateRepository.findByLicense_IdAndDeletedAtIsNull(licenseId);
    }

    /**
     * Retrieves a single certificate by its ID, provided it has not been marked as deleted.
     *
     * @param id the ID of the certificate to retrieve; must not be null.
     * @return the certificate associated with the given ID that has not been marked as deleted.
     * @throws ResponseStatusException if the ID is null or if the certificate could not be found.
     */
    @Transactional
    public Certificate findOne(Integer id) {
        if (id == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, CERTIFICATE_ID_IS_INVALID);
        }

        return certificateRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CERTIFICATE_NOT_FOUND));
    }

    /**
     * Creates a new certificate based on the provided data transfer object.
     * The created certificate is associated with the license specified in the DTO,
     * and its status is set to "NOT_AVAILABLE" by default.
     *
     * @param dto the data transfer object containing the information required to create the certificate;
     *            must include the license ID and name of the certificate.
     * @return the newly created certificate after it has been saved to the repository.
     * @throws ResponseStatusException if the license specified in the DTO is not found or is marked as deleted.
     */
    @Transactional
    public Certificate create(CreateCertificateDto dto) {
        License license = licenseRepository.findByIdAndDeletedAtIsNull(dto.getLicenseId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "License not found"));

        Certificate certificate = new Certificate();
        certificate.setLicense(license);
        certificate.setName(dto.getName());
        certificate.setStatus(Certificate.Status.NOT_AVAILABLE);

        return certificateRepository.save(certificate);
    }

    /**
     * Updates the name of an existing certificate if the certificate exists and has not been marked as deleted.
     *
     * @param id  the ID of the certificate to update; must not be null.
     * @param dto the new name to assign to the certificate; must not be null.
     * @return the updated certificate entity after saving the changes.
     * @throws ResponseStatusException if the ID is null, the certificate does not exist, or it has been marked as deleted.
     */
    @Transactional
    public Certificate update(Integer id,
                              UpdateCertificateDto dto) {
        if (id == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, CERTIFICATE_ID_IS_INVALID);
        }

        Certificate certificate = certificateRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CERTIFICATE_NOT_FOUND));

        certificate.setName(dto.getName());

        return certificateRepository.save(certificate);
    }

    /**
     * Marks a certificate as deleted by setting its deletion timestamp, provided the certificate
     * exists and has not already been marked as deleted.
     *
     * @param id the ID of the certificate to be marked as deleted; must not be null.
     * @throws ResponseStatusException if the ID is null or the certificate does not exist.
     */
    @Transactional
    public void remove(Integer id) {
        if (id == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, CERTIFICATE_ID_IS_INVALID);
        }

        Certificate certificate = certificateRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CERTIFICATE_NOT_FOUND));

        certificate.setDeletedAt(LocalDate.now());

        certificateRepository.save(certificate);
    }

    /**
     * Generates a PDF document for a certificate using the provided data transfer object.
     * The PDF includes company information, certificate details, date, and signatures.
     *
     * @param dto the data transfer object containing the certificate details including
     *            company information, certificate ID, and address details; must not be null.
     * @return a byte array representing the generated PDF document.
     */
    @Transactional
    public byte[] generateCertificatePdf(CreateCertificatePdfDto dto) {
        LocalDate now = LocalDate.now();
        String today = now.format(DateTimeFormatter.ofPattern("dd.MM.yyyy"));

        Map<String, Object> data = new HashMap<>();
        data.put("logoLizPath", pdfService.getImageAsBase64("logo_liz.png"));
        data.put("logoInterPath", pdfService.getImageAsBase64("logo_inter.png"));

        Map<String, Object> signature = new HashMap<>();
        signature.put("firstName", "Markus Müller-Drexel");
        signature.put("firstSignature", pdfService.getImageAsBase64("first_signature.png"));
        signature.put("secondName", "Frank Kurrat");
        signature.put("secondSignature", pdfService.getImageAsBase64("second_signature.png"));

        data.put("signature", signature);
        data.put("date", today);
        data.put("agreementNumber", dto.getCertificateId());
        data.put("companyName", dto.getCompany().getName());
        data.put("lucid", dto.getCompany().getLucid());

        if (dto.getCompany().getAddress() != null) {
            data.put("street", dto.getCompany().getAddress().getStreetAndNumber());
            data.put("zip", dto.getCompany().getAddress().getZipCode());
            data.put("country", dto.getCompany().getAddress().getCountryCode());
        }

        data.put("year", now.getYear());


        return pdfService.generatePdf(data);
    }

}
