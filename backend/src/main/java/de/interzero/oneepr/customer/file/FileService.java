package de.interzero.oneepr.customer.file;

import de.interzero.oneepr.customer.certificate.CertificateRepository;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.file.dto.CreateFileDto;
import de.interzero.oneepr.customer.file.dto.LambdaPresignedResponseDto;
import de.interzero.oneepr.customer.file.dto.RequestPresignedUrlDto;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.license_required_information.LicenseRequiredInformationRepository;
import de.interzero.oneepr.customer.market_material.MarketMaterialRepository;
import de.interzero.oneepr.customer.termination.TerminationRepository;
import de.interzero.oneepr.common.service.AwsService;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.io.IOException;
import java.security.SecureRandom;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Service
@RequiredArgsConstructor
public class FileService {

    private final FileRepository fileRepository;

    private final LicenseRequiredInformationRepository licenseRequiredInformationRepository;

    private final ContractRepository contractRepository;

    private final CertificateRepository certificateRepository;

    private final LicenseRepository licenseRepository;

    private final TerminationRepository terminationRepository;

    private final MarketMaterialRepository marketMaterialRepository;

    private final AwsService awsService;

    private static final long MAX_FILE_SIZE = 25L * 1024L * 1024L; // 25MB

    private static final String CHARACTERS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    private static final SecureRandom RANDOM = new SecureRandom();

    // File relation constants matching the TypeScript implementation
    private static final Set<String> VALID_FILE_RELATIONS = Set.of(
            "required_information_id",
            "contract_id",
            "certificate_id",
            "license_id",
            "termination_id",
            "general_information_id",
            "third_party_invoice_id",
            "marketing_material_id",
            "partner_contract_id",
            "order_id");

    @Getter
    public static class FileWithBuffer {

        private final File file;

        private final byte[] buffer;

        public FileWithBuffer(File file,
                              byte[] buffer) {
            this.file = file;
            this.buffer = buffer;
        }
    }

    /**
     * Upload file to S3 and database
     * @ts-legacy the original code do not have the order table so we just set the order_id to file
     * @param data     the file information and related ids
     * @param file     the bytes file
     * @param userId   the user id
     * @param userRole the user role
     * @return The found File entity.
     * @throws IOException if the file handling throws exceptions
     */
    @Transactional(timeout = 45)
    public File uploadFile(CreateFileDto data,
                           MultipartFile file,
                           String userId,
                           String userRole) throws IOException {
        long size = file.getSize();
        String originalname = file.getOriginalFilename();
        String mimetype = file.getContentType();

        String filename = generateFilename(data.getType(), originalname);

        if (size > MAX_FILE_SIZE) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "File size cannot exceed 25MB");
        }

        // Upload file using the common AwsService
        try {
            awsService.uploadFile(filename, file, mimetype);
            log.info("Successfully uploaded file {} to S3 bucket {}", filename, awsService.getBucketName());
        } catch (Exception error) {
            log.error("Error uploading file to S3: {}", error.getMessage(), error);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to upload file to S3");
        }

        // Create file record in database
        File fileEntity = new File();
        fileEntity.setUserId(userId);
        fileEntity.setSize(String.valueOf(size));
        fileEntity.setName(filename);
        fileEntity.setExtension(mimetype);
        fileEntity.setOriginalName(originalname);
        fileEntity.setRequiredInformation(data.getRequiredInformationId() == null ? null : licenseRequiredInformationRepository.findById(
                data.getRequiredInformationId()).orElse(null));
        fileEntity.setContract(data.getContractId() == null ? null : contractRepository.findById(data.getContractId())
                .orElse(null));
        fileEntity.setCertificate(data.getCertificateId() == null ? null : certificateRepository.findById(data.getCertificateId())
                .orElse(null));
        fileEntity.setLicense(data.getLicenseId() == null ? null : licenseRepository.findById(data.getLicenseId())
                .orElse(null));
        fileEntity.setTermination(data.getTerminationId() == null ? null : terminationRepository.findById(data.getTerminationId())
                .orElse(null));
        fileEntity.setMarketingMaterial(data.getMarketingMaterialId() == null ? null : marketMaterialRepository.findById(
                data.getMarketingMaterialId()).orElse(null));
        fileEntity.setType(data.getType());
        fileEntity.setOrderId(data.getOrderId() == null ? null : data.getOrderId());
        fileEntity.setCreatedAt(Instant.now());
        fileEntity.setUpdatedAt(Instant.now());

        // Set relationship IDs - these would need to be resolved to actual entities
        // For now, we'll set them as null and let the application handle the relationships
        return fileRepository.save(fileEntity);
    }

    public FileWithBuffer getFile(String fileId) {
        Optional<File> fileOptional = fileRepository.findById(fileId);

        if (fileOptional.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "File not found");
        }

        File file = fileOptional.get();

        try {
            // Download file using the common AwsService
            AwsService.FileDownloadResponse downloadResponse = awsService.downloadFile(file.getName());

            log.info("Successfully downloaded file {} from S3 bucket {}", file.getName(), awsService.getBucketName());
            return new FileWithBuffer(file, downloadResponse.getContent());
        } catch (ResponseStatusException e) {
            // Re-throw ResponseStatusException as-is
            throw e;
        } catch (Exception e) {
            log.error("Error downloading file from S3", e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Error downloading file from S3");
        }
    }

    /**
     * Get file by relation and related entity ID
     *
     * @param relation   the relation type (e.g., "required_information_id", "contract_id", etc.)
     * @param relativeId the ID of the related entity
     * @return FileWithBuffer containing the file and its content
     */
    public FileWithBuffer getFileByRelative(String relation,
                                            String relativeId) {
        // Validate relation
        if (!VALID_FILE_RELATIONS.contains(relation)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid relation");
        }

        // Find file by relation using the repository method
        Optional<File> fileOptional = fileRepository.findByRelation(relation, Integer.parseInt(relativeId));

        if (fileOptional.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "File not found");
        }

        File file = fileOptional.get();

        try {
            // Download file directly from S3 using AWS SDK
            byte[] fileContent = awsService.downloadFileContent(file.getName());

            log.info("Successfully downloaded file {} from S3 bucket by relation", file.getName());
            return new FileWithBuffer(file, fileContent);
        } catch (Exception e) {
            log.error("Error downloading file from S3 by relation", e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Error downloading file from S3");
        }
    }

    @Transactional
    public void deleteFile(String fileId) {
        Optional<File> fileOptional = fileRepository.findById(fileId);

        if (fileOptional.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "File not found");
        }

        File file = fileOptional.get();
        file.setDeletedAt(LocalDate.now());
        fileRepository.save(file);
    }

    public LambdaPresignedResponseDto requestUrl(RequestPresignedUrlDto requestPresignedUrlDto) {
        try {
            String filename = requestPresignedUrlDto.getFilename();
            String fileType = requestPresignedUrlDto.getFileType();

            // Use the common AwsService to generate presigned URL
            AwsService.PresignedUrlResponse presignedResponse = awsService.generatePresignedUploadUrl(
                    filename,
                    fileType);

            // Create response DTO
            LambdaPresignedResponseDto response = new LambdaPresignedResponseDto();
            response.setUploadUrl(presignedResponse.getPreSignedUrl());
            response.setFields(new HashMap<>(presignedResponse.getFields()));

            return response;
        } catch (Exception e) {
            log.error("Error generating presigned URL", e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Problems generating url");
        }
    }

    private String generateFilename(File.Type docType,
                                    String filename) {
        LocalDate today = LocalDate.now();
        String year = String.valueOf(today.getYear());
        String month = today.format(DateTimeFormatter.ofPattern("MM"));
        String day = today.format(DateTimeFormatter.ofPattern("dd"));

        String hash = generateHash();
        return String.format("%s/%s/%s/%s/%s-%s", docType, year, month, day, hash, filename);
    }

    private String generateHash() {
        StringBuilder hash = new StringBuilder(8);
        for (int i = 0; i < 8; i++) {
            hash.append(CHARACTERS.charAt(RANDOM.nextInt(CHARACTERS.length())));
        }
        return hash.toString();
    }
}
