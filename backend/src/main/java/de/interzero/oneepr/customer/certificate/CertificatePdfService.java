package de.interzero.oneepr.customer.certificate;

import com.github.jknack.handlebars.Handlebars;
import com.github.jknack.handlebars.Helper;
import com.github.jknack.handlebars.Options;
import com.github.jknack.handlebars.Template;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UncheckedIOException;
import java.util.Base64;
import java.util.Map;

@Service
public class CertificatePdfService {

    private static final Logger log = LoggerFactory.getLogger(CertificatePdfService.class);

    private final Handlebars handlebars;

    public CertificatePdfService() {
        this.handlebars = new Handlebars();
        registerHelpers();
    }

    /**
     * Generates a PDF document based on the provided data model.
     *
     * @param data a map containing the data required for populating the PDF template
     * @return a byte array representing the generated PDF document
     * @throws IllegalStateException if an error occurs during PDF generation
     */
    public byte[] generatePdf(Map<String, Object> data) {
        long start = System.currentTimeMillis();

        try {
            Template template = handlebars.compile("templates/certificate-direct");
            String html = template.apply(data);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            PdfRendererBuilder builder = new PdfRendererBuilder();
            builder.useFastMode();
            builder.withHtmlContent(html, null);
            builder.toStream(outputStream);
            builder.run();

            long end = System.currentTimeMillis();
            log.info("PDF generated in {} ms", (end - start));

            return outputStream.toByteArray();

        } catch (Exception e) {
            throw new IllegalStateException("Error generating certificate PDF", e);
        }
    }

    /**
     * Converts an image from the specified relative path to a Base64-encoded string.
     *
     * @param relativePath the relative path to the image file, located under the directory
     *                     "static/images/"
     * @return a Base64-encoded string representation of the image, prefixed with the
     * MIME type ("data:image/png;base64,")
     * @throws UncheckedIOException if an I/O error occurs while reading the image file
     */
    public String getImageAsBase64(String relativePath) {
        try {
            ClassPathResource imageFile = new ClassPathResource("static/images/" + relativePath);
            if (!imageFile.exists()) {
                throw new UncheckedIOException(
                        "Image file not found: " + relativePath,
                                               new IOException("Resource does not exist"));
            }

            byte[] bytes = imageFile.getInputStream().readAllBytes();
            return "data:image/png;base64," + Base64.getEncoder().encodeToString(bytes);
        } catch (IOException e) {
            throw new UncheckedIOException("Could not load image " + relativePath, e);
        }
    }

    /**
     * Registers custom helper methods for use within Handlebars templates.
     * These helpers extend the default functionality of Handlebars by
     * providing additional comparison, logical, and formatting utilities.
     * <p>
     * Helper methods include:
     * - "eq": Compares two values for equality.
     * - "ne": Compares two values for inequality.
     * - "gt": Checks if a value is greater than another.
     * - "lt": Checks if a value is less than another.
     * - "gte": Checks if a value is greater than or equal to another.
     * - "lte": Checks if a value is less than or equal to another.
     * - "and": Performs a logical AND operation on two boolean-like values.
     * - "or": Performs a logical OR operation on two boolean-like values.
     * - "breaklines": Converts line breaks in a string to HTML <br> tags.
     * </p>
     * These helpers enhance the templating capabilities for use in
     * generating dynamic content such as HTML or PDFs.
     */
    private void registerHelpers() {
        handlebars.registerHelper(
                "eq",
                (Helper<Object>) (a, options) -> boolToBlock(a.equals(options.param(0)), options));
        handlebars.registerHelper(
                "ne",
                (Helper<Object>) (a, options) -> boolToBlock(!a.equals(options.param(0)), options));
        handlebars.registerHelper(
                "gt",
                (Helper<Object>) (a, options) -> boolToBlock(
                        asNumber(a) > asNumber(options.param(0)),
                        options));
        handlebars.registerHelper(
                "lt",
                (Helper<Object>) (a, options) -> boolToBlock(
                        asNumber(a) < asNumber(options.param(0)),
                        options));
        handlebars.registerHelper(
                "gte",
                (Helper<Object>) (a, options) -> boolToBlock(
                        asNumber(a) >= asNumber(options.param(0)),
                        options));
        handlebars.registerHelper(
                "lte",
                (Helper<Object>) (a, options) -> boolToBlock(
                        asNumber(a) <= asNumber(options.param(0)),
                        options));
        handlebars.registerHelper(
                "and",
                (Helper<Object>) (a, options) -> boolToBlock(
                        toBool(a) && toBool(options.param(0)),
                        options));
        handlebars.registerHelper(
                "or",
                (Helper<Object>) (a, options) -> boolToBlock(
                        toBool(a) || toBool(options.param(0)),
                        options));

        handlebars.registerHelper(
                "breaklines", (Helper<String>) (text, options) -> {
                    if (text == null) {
                        return "";
                    }
                    String escaped = (String) Handlebars.Utils.escapeExpression(text);
                    String formatted = escaped.replaceAll("(\r\n|\n|\r)", "<br>");
                    return new Handlebars.SafeString(formatted);
                });
    }

    /**
     * Determines which block of a Handlebars template to execute based on a boolean condition.
     * If the condition is true, the "fn" block is executed; otherwise, the "inverse" block is executed.
     *
     * @param condition the boolean condition to evaluate
     * @param options   the Handlebars options context, containing the blocks to execute
     * @return the result of executing the appropriate block, either "fn" or "inverse"
     * @throws IOException if an error occurs while rendering the block
     */
    private Object boolToBlock(boolean condition,
                               Options options) throws IOException {
        return condition ? options.fn() : options.inverse();
    }

    /**
     * Converts an object to a boolean value based on its type.
     *
     * @param obj the object to be converted; expected types include Boolean, Number,
     *            or other non-null objects. Boolean values are returned as-is.
     *            Numbers are considered true if their double value is not zero.
     *            Any non-null object is considered true.
     * @return a boolean representation of the provided object; true for non-null objects
     * and non-zero numbers, and false otherwise.
     */
    private boolean toBool(Object obj) {
        if (obj instanceof Boolean bool) {
            return bool;
        }
        if (obj instanceof Number num) {
            return num.doubleValue() != 0;
        }
        return obj != null;
    }

    /**
     * Converts the provided object to a double-precision number.
     * If the object is an instance of {@code Number}, its numeric value is directly returned.
     * Otherwise, it attempts to parse the object's string representation as a {@code double}.
     * If parsing fails, the method returns {@code 0}.
     *
     * @param obj the object to be converted into a double; can be a {@code Number}, or an object
     *            with a valid numeric string representation
     * @return the numeric value of the object as a {@code double}, or {@code 0} if conversion fails
     */
    private double asNumber(Object obj) {
        if (obj instanceof Number num) {
            return num.doubleValue();
        }
        try {
            return Double.parseDouble(obj.toString());
        } catch (Exception e) {
            return 0;
        }
    }
}
