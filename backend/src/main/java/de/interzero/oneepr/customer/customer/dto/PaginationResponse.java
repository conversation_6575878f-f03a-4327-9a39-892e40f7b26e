package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class PaginationResponse<T> {

    @JsonProperty("customers")
    private List<T> customers;

    @JsonProperty("pages")
    private Integer pages;

    @JsonProperty("count")
    private Long count;

    @JsonProperty("current_page")
    private Integer currentPage;
}
