package de.interzero.oneepr.customer.customer;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.cluster.ClusterCustomer;
import de.interzero.oneepr.customer.commission.Commission;
import de.interzero.oneepr.customer.company.Company;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.customer_activity.CustomerActivity;
import de.interzero.oneepr.customer.customer_commitment.CustomerCommitment;
import de.interzero.oneepr.customer.customer_consent.CustomerConsent;
import de.interzero.oneepr.customer.customer_document.CustomerDocument;
import de.interzero.oneepr.customer.customer_invite_token.CustomerInviteToken;
import de.interzero.oneepr.customer.customer_phone.CustomerPhone;
import de.interzero.oneepr.customer.entity.CouponCustomer;
import de.interzero.oneepr.customer.entity.CustomerTutorial;
import de.interzero.oneepr.customer.invite.CustomerInvitation;
import de.interzero.oneepr.customer.recommend_country.RecommendedCountry;
import de.interzero.oneepr.customer.shopping_cart.ShoppingCart;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "customer",
        schema = "public"
)
public class Customer {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "customer_id_gen"
    )
    @SequenceGenerator(
            name = "customer_id_gen",
            sequenceName = "customer_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "type",
            nullable = false
    )
    @JsonProperty("type")
    private Customer.Type type = Type.REGULAR;

    @NotNull
    @Column(
            name = "first_name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("first_name")
    private String firstName;

    @NotNull
    @Column(
            name = "last_name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("last_name")
    private String lastName;

    @Column(
            name = "salutation",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("salutation")
    private String salutation;

    @NotNull
    @Column(
            name = "email",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("email")
    private String email;

    @NotNull
    @Column(
            name = "user_id",
            nullable = false
    )
    @JsonProperty("user_id")
    private Integer userId;

    @Column(name = "is_active")
    @JsonProperty("is_active")
    private Boolean isActive;

    @Column(name = "document_id")
    @JsonProperty("document_id")
    private Integer documentId;

    @Column(
            name = "id_stripe",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("id_stripe")
    private String idStripe;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @Column(
            name = "company_name",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("company_name")
    private String companyName;

    @Column(
            name = "language",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("language")
    private String language;

    @Column(
            name = "currency",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("currency")
    private String currency;

    @OneToMany(
            mappedBy = "customer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("clusters")
    @JsonIgnore
    private List<ClusterCustomer> clusters;

    @OneToMany(
            mappedBy = "orderCustomer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("commissions")
    @JsonIgnore
    private List<Commission> commissions = new ArrayList<>();

    @OneToMany(
            mappedBy = "customer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("companies")
    @JsonIgnore
    private List<Company> companies = new ArrayList<>();

    @OneToMany(
            mappedBy = "customer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("contracts")
    @JsonIgnore
    private List<Contract> contracts = new ArrayList<>();

    @OneToMany(
            mappedBy = "customer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("coupons")
    @JsonIgnore
    private List<CouponCustomer> coupons = new ArrayList<>();


    @OneToMany(
            mappedBy = "customer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("customer_commitment")
    @JsonIgnore
    private List<CustomerCommitment> customerCommitment = new ArrayList<>();

    @OneToMany(
            mappedBy = "customer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("consents")
    @JsonIgnore
    private List<CustomerConsent> consents = new ArrayList<>();

    @OneToMany(
            mappedBy = "customer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("documents")
    @JsonIgnore
    private List<CustomerDocument> documents = new ArrayList<>();

    @OneToMany(
            mappedBy = "customer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("sent_invitations")
    @JsonIgnore
    private List<CustomerInvitation> sentInvitations = new ArrayList<>();

    @OneToMany(
            mappedBy = "invitedCustomer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("received_invitations")
    @JsonIgnore
    private List<CustomerInvitation> receivedInvitations = new ArrayList<>();

    @OneToOne(mappedBy = "customer")
    @JsonProperty("invite_token")
    @JsonIgnore
    private CustomerInviteToken inviteToken;

    @OneToMany(
            mappedBy = "customer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("phones")
    @JsonIgnore
    private List<CustomerPhone> phones = new ArrayList<>();

    @OneToMany(
            mappedBy = "customer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("customer_tutorial")
    @JsonIgnore
    private List<CustomerTutorial> customerTutorial = new ArrayList<>();

    @OneToMany(
            mappedBy = "customer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("recommended_countries")
    @JsonIgnore
    private List<RecommendedCountry> recommendedCountries = new ArrayList<>();

    @OneToMany(
            mappedBy = "affiliateCustomer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("affiliate_shopping_cart")
    @JsonIgnore
    private List<ShoppingCart> affiliatedShoppingCarts = new ArrayList<>();

    @OneToMany(
            mappedBy = "customer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("activities")
    @JsonIgnore
    private List<CustomerActivity> activities = new ArrayList<>();

    @OneToMany(
            mappedBy = "customer",
            fetch = FetchType.LAZY
    )
    @JsonProperty("shopping_carts")
    @JsonIgnore
    private List<ShoppingCart> shoppingCarts = new ArrayList<>();

    public enum Type {
        REGULAR,
        PREMIUM
    }

    public void addContract(Contract contract){
        this.contracts.add(contract);
    }


    public void addCompany(Company company){
        this.companies.add(company);
    }
}