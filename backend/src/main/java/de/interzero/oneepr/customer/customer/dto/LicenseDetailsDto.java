package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.license.License;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * Data Transfer Object representing a projection of a License.
 * <p>
 * This DTO is a direct translation of the {@code select} clause on the
 * {@code licenses} relation in the {@code customer.service.ts#details} method.
 * It includes specific fields and filtered nested relations.
 */
@Data
@NoArgsConstructor
public class LicenseDetailsDto {

    @Schema(description = "Unique identifier of the license.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "The unique identifier of the country.")
    @JsonProperty("country_id")
    private Integer countryId;

    @Schema(description = "The ISO code of the country.")
    @JsonProperty("country_code")
    private String countryCode;

    @Schema(description = "The name of the country.")
    @JsonProperty("country_name")
    private String countryName;

    @Schema(description = "The flag icon or URL for the country.")
    @JsonProperty("country_flag")
    private String countryFlag;

    @Schema(description = "The year the license is valid for.")
    @JsonProperty("year")
    private Integer year;

    @Schema(description = "The registration status of the license.")
    @JsonProperty("registration_status")
    private License.RegistrationStatus registrationStatus;

    @Schema(description = "The contract status of the license.")
    @JsonProperty("contract_status")
    private License.ContractStatus contractStatus;

    @Schema(description = "The clerk control status of the license.")
    @JsonProperty("clerk_control_status")
    private License.ClerkControlStatus clerkControlStatus;

    // Timestamps from the base License model
    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "The associated termination details, if any.")
    @JsonProperty("termination")
    private TerminationDetailsDto termination;

    @Schema(description = "List of non-deleted files associated with this license.")
    @JsonProperty("files")
    private List<FileDetailsDto> files;

    @Schema(description = "List of non-deleted certificates associated with this license.")
    @JsonProperty("certificates")
    private List<CertificateDetailsDto> certificates;
}