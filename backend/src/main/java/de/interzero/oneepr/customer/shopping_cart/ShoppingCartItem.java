package de.interzero.oneepr.customer.shopping_cart;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.contract.Contract;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Map;

@Getter
@Setter
@Entity
@Table(
        name = "shopping_cart_item",
        schema = "public"
)
public class ShoppingCartItem {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "shopping_cart_item_id_gen"
    )
    @SequenceGenerator(
            name = "shopping_cart_item_id_gen",
            sequenceName = "shopping_cart_item_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "shopping_cart_id",
            nullable = false
    )
    @JsonIgnore
    private ShoppingCart shoppingCart;

    @NotNull
    @Column(
            name = "country_id",
            nullable = false
    )
    @JsonProperty("country_id")
    private Integer countryId;

    @NotNull
    @Column(
            name = "country_code",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("country_code")
    private String countryCode;

    @NotNull
    @Column(
            name = "country_name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("country_name")
    private String countryName;

    @NotNull
    @Column(
            name = "country_flag",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("country_flag")
    private String countryFlag;

    @NotNull
    @Column(
            name = "year",
            nullable = false
    )
    @JsonProperty("year")
    private Integer year;

    @NotNull
    @Column(
            name = "price_list",
            nullable = false
    )
    @JdbcTypeCode(SqlTypes.JSON)
    @JsonProperty("price_list")
    private Map<String, Object> priceList;

    @Column(name = "packaging_services")
    @JdbcTypeCode(SqlTypes.JSON)
    @JsonProperty("packaging_services")
    private Object packagingServices;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @Column(name = "calculator")
    @JdbcTypeCode(SqlTypes.JSON)
    @JsonProperty("calculator")
    private Map<String, Object> calculator;

    @Column(name = "price")
    @JsonProperty("price")
    private Integer price;

    @ColumnDefault("'PURCHASE'")
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "service_type")
    @JsonProperty("service_type")
    private Contract.Type serviceType;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "specification_type")
    @JsonProperty("specification_type")
    private SpecificationType specificationType;

    @PrePersist
    protected void onCreate() {
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }

    public enum SpecificationType {
        PURCHASE,
        VOLUME_CHANGE
    }
}