package de.interzero.oneepr.admin.country;

import de.interzero.oneepr.admin.country.dto.CountryResponseDto;

/**
 * A utility class for mapping {@link Country} entities to their corresponding DTOs.
 * This class contains static methods for conversion and cannot be instantiated.
 */
public final class CountryMapper {

    /**
     * Private constructor to prevent instantiation of this utility class.
     *
     * @throws IllegalStateException if an attempt is made to instantiate this class.
     */
    private CountryMapper() {
        throw new IllegalStateException("Utility class cannot be instantiated");
    }

    /**
     * Converts a {@link Country} entity to a {@link CountryResponseDto}.
     * <p>
     * This method performs a direct field-to-field mapping from the entity to the DTO.
     * It does not map any of the relational fields, focusing only on the country's own data.
     *
     * @param country The non-null {@link Country} entity to convert.
     * @return A new {@link CountryResponseDto} instance populated with data from the entity.
     */
    public static CountryResponseDto toResponseDto(Country country) {
        CountryResponseDto dto = new CountryResponseDto();

        dto.setId(country.getId());
        dto.setName(country.getName());
        dto.setCreatedAt(country.getCreatedAt());
        dto.setUpdatedAt(country.getUpdatedAt());
        dto.setAuthorizeRepresentativeObligated(country.getAuthorizeRepresentativeObligated());
        dto.setCode(country.getCode());
        dto.setFlagUrl(country.getFlagUrl());
        dto.setOtherCostsObligated(country.getOtherCostsObligated());
        dto.setIsPublished(country.getIsPublished());

        return dto;
    }
}