package de.interzero.oneepr.admin.required_information;

import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.criteria.Criteria;
import de.interzero.oneepr.admin.criteria.CriteriaRepository;
import de.interzero.oneepr.admin.fraction_icon.FilesRepository;
import de.interzero.oneepr.admin.required_information.dto.CreateRequiredInformationDto;
import de.interzero.oneepr.admin.required_information.dto.UpdateRequiredInformationDto;
import de.interzero.oneepr.common.config.ModelMapperConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;

/**
 * Service class for managing Required Information business logic.
 */
@Service
@RequiredArgsConstructor
public class RequiredInformationService {

    private final RequiredInformationRepository requiredInformationRepository;

    private final CountryRepository countryRepository;

    private final FilesRepository filesRepository;

    private final CriteriaRepository criteriaRepository;

    private static final String NOT_FOUND = "Required information not found";

    private static final String COUNTRY_NOT_FOUND = "Country not found";

    private static final String FILE_NOT_FOUND = "File not found";

    /**
     * Creates a new RequiredInformation entry.
     *
     * @param createDto DTO with data for the new entry.
     * @return The newly created and persisted RequiredInformation entity.
     */
    @Transactional
    public RequiredInformation create(CreateRequiredInformationDto createDto) {
        RequiredInformation newInfo = ModelMapperConfig.mapPresentFields(createDto, RequiredInformation.class);

        if (createDto.getCountryId() != null) {
            var country = countryRepository.findById(createDto.getCountryId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, COUNTRY_NOT_FOUND));
            newInfo.setCountry(country);
        }

        if (createDto.getFileId() != null) {
            var file = filesRepository.findById(createDto.getFileId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, FILE_NOT_FOUND));
            newInfo.setFiles(file);
        }

        return requiredInformationRepository.save(newInfo);
    }

    /**
     * Retrieves all non-deleted required information entries.
     *
     * @return A list of active RequiredInformation entities.
     */
    @Transactional(readOnly = true)
    public List<RequiredInformation> findAll() {
        return requiredInformationRepository.findAllByDeletedAtIsNull();
    }

    /**
     * Retrieves a single, non-deleted required information entry by its ID.
     *
     * @param id The ID of the entry to retrieve.
     * @return The found RequiredInformation entity.
     */
    @Transactional(readOnly = true)
    public RequiredInformation findOne(Integer id) {
        return requiredInformationRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, NOT_FOUND));
    }

    /**
     * Partially updates an existing required information entry.
     *
     * @param id        The ID of the entry to update.
     * @param updateDto DTO containing the fields to update.
     * @return The updated RequiredInformation entity.
     */
    @Transactional
    public RequiredInformation update(Integer id,
                                      UpdateRequiredInformationDto updateDto) {
        RequiredInformation info = requiredInformationRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, NOT_FOUND));

        ModelMapperConfig.mapPresentFields(updateDto, info);

        if (updateDto.getCountryId() != null) {
            var country = countryRepository.findById(updateDto.getCountryId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, COUNTRY_NOT_FOUND));
            info.setCountry(country);
        }

        if (updateDto.getFileId() != null) {
            var file = filesRepository.findById(updateDto.getFileId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, FILE_NOT_FOUND));
            info.setFiles(file);
        }

        return requiredInformationRepository.save(info);
    }

    /**
     * Soft-deletes a required information entry and all of its direct child criteria.
     *
     * @param id The ID of the entry to soft-delete.
     */
    @Transactional
    public void remove(Integer id) {
        RequiredInformation infoToRemove = requiredInformationRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, NOT_FOUND));

        List<Criteria> relatedCriteria = criteriaRepository.findByRequiredInformation_Id(id);

        Instant deletionTime = Instant.now();

        infoToRemove.setDeletedAt(deletionTime);
        relatedCriteria.forEach(criteria -> criteria.setDeletedAt(deletionTime));

        requiredInformationRepository.save(infoToRemove);
        criteriaRepository.saveAll(relatedCriteria);
    }
}