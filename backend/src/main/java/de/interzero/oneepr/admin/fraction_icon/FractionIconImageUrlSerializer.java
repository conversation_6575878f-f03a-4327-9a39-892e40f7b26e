package de.interzero.oneepr.admin.fraction_icon;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.ser.PropertyWriter;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import de.interzero.oneepr.admin.service_setup.dto.FractionIconNestedDto;
import de.interzero.oneepr.admin.report_set.dto.FractionIconDetailDto;
import de.interzero.oneepr.common.service.AwsService;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;
import java.time.Duration;

@Slf4j
public class FractionIconImageUrlSerializer extends SimpleBeanPropertyFilter {

    public final AwsService awsService;

    public FractionIconImageUrlSerializer(AwsService awsService) {
        this.awsService = awsService;
    }


    @Override
    public void serializeAsField(Object pojo,
                                 JsonGenerator jgen,
                                 SerializerProvider provider,
                                 PropertyWriter writer) throws Exception {

        if ("image_url".equals(writer.getName())) {
            String imageUrl = getImageUrlFromObject(pojo);
            if (imageUrl != null) {
                // If the image URL is already an absolute URL, no need to presign it
                // Skip presigning for absolute URLs
                if (imageUrl.startsWith("https://")) {
                    jgen.writeStringField(writer.getName(), imageUrl);
                    return;
                }
                //awsService.generatePresignedDownloadUrl will not call any aws api to generate the url,
                // instead it will just generate the url in our local from the access key and access token
                String preSignedUrl = awsService.generatePresignedDownloadUrl(imageUrl, Duration.ofMinutes(15))
                        .getPreSignedUrl();
                jgen.writeStringField(writer.getName(), preSignedUrl);
                return;
            }
        }
        writer.serializeAsField(pojo, jgen, provider);
    }

    /**
     * Extracts the image URL from various types of objects that contain fraction icon data.
     * Supports FractionIcon entity and various DTOs like FractionIconNestedDto, FractionIconDetailDto, etc.
     *
     * @param pojo The object to extract the image URL from
     * @return The image URL string, or null if not found or not applicable
     */
    private String getImageUrlFromObject(Object pojo) {
        return switch (pojo) {
            case null -> null;
            // Handle FractionIcon entity
            case FractionIcon icon -> icon.getImageUrl();
            // Handle known DTO types
            case FractionIconNestedDto dto -> dto.getImageUrl();
            case FractionIconDetailDto dto -> dto.getImageUrl();
            default -> getImageUrlViaReflection(pojo);
            // Handle any other object that might have an imageUrl field using reflection
        };

    }

    /**
     * Uses reflection to get the imageUrl from any object that has a getImageUrl() method.
     * This provides a fallback for any DTO that follows the standard naming convention.
     *
     * @param pojo The object to extract the image URL from
     * @return The image URL string, or null if not found
     */
    private String getImageUrlViaReflection(Object pojo) {
        try {
            Method getImageUrlMethod = pojo.getClass().getMethod("getImageUrl");
            Object result = getImageUrlMethod.invoke(pojo);
            if (result instanceof String url) {
                return url;
            }
            return null;
        } catch (NoSuchMethodException | SecurityException e) {
            // Method not found or invocation failed - this is expected for objects that don't have imageUrl
            log.warn("No getImageUrl() on type {}", pojo.getClass().getName());
            return null;
        } catch (ReflectiveOperationException e) {
            log.warn("Failed invoking getImageUrl() on type {}: {}", pojo.getClass().getName(), e.toString());
            return null;
        }
    }
}
