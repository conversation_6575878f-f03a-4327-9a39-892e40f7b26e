package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Represents the nested fraction icon object within a ReportSetFraction response.
 */
@Data
@JsonFilter("fractionIconFilter")
public class FractionIconNestedDto {

    @Schema(description = "The unique identifier of the fraction icon.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "The public URL of the icon image.")
    @JsonProperty("image_url")
    private String imageUrl;
}
