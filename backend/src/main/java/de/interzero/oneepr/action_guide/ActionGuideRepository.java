package de.interzero.oneepr.action_guide;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

@Repository
public interface ActionGuideRepository extends JpaRepository<ActionGuide, Integer> {

    /**
     * Finds all action guides for a specific contract that have not been soft-deleted.
     *
     * @param contractId The ID of the contract to filter by.
     * @return A list of non-deleted action guides for the given contract.
     */
    List<ActionGuide> findAllByContract_IdAndDeletedAtIsNull(Integer contractId);

    /**
     * Performs a bulk update to reactivate multiple ActionGuide entities at once.
     * <p>
     * This sets the contractStatus to 'ACTIVE' and updates the 'updatedAt' timestamp
     * for all action guides whose IDs are in the provided list.
     *
     * @param ids A list of ActionGuide entity IDs to reactivate.
     * @param now The current timestamp to set for the 'updatedAt' field.
     * @ts-legacy This bulk update method is the direct JPA equivalent of the
     * {@code tx.actionGuide.updateMany({ where: { id: { in: ... } } })} operation
     * found in the TypeScript source code.
     */
    @Modifying
    @Query("UPDATE ActionGuide ag SET ag.contractStatus = 'ACTIVE', ag.updatedAt = :now WHERE ag.id IN :ids")
    void reactivateAllByIds(@Param("ids") List<Integer> ids,
                            @Param("now") Instant now);
}
