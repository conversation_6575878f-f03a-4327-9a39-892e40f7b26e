package de.interzero.oneepr.admin.fraction_icon;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.PropertyWriter;
import de.interzero.oneepr.admin.report_set.dto.FractionIconDetailDto;
import de.interzero.oneepr.admin.service_setup.dto.FractionIconNestedDto;
import de.interzero.oneepr.common.service.AwsService;
import lombok.Getter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for {@link FractionIconImageUrlSerializer}.
 * Tests all conditions and branches in the serializer logic.
 */
@ExtendWith(MockitoExtension.class)
class FractionIconImageUrlSerializerTest {

    @Mock
    private AwsService awsService;

    @Mock
    private JsonGenerator jsonGenerator;

    @Mock
    private SerializerProvider serializerProvider;

    @Mock
    private PropertyWriter propertyWriter;

    @Mock
    private AwsService.PresignedUrlResponse presignedUrlResponse;

    private FractionIconImageUrlSerializer serializer;

    @BeforeEach
    void setUp() {
        serializer = new FractionIconImageUrlSerializer(awsService);
    }

    // --- serializeAsField method tests ---

    @Test
    void serializeAsField_whenWriterNameIsNotImageUrl_shouldCallWriterSerializeAsField() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("other_field");
        Object testPojo = new Object();

        // Act
        serializer.serializeAsField(testPojo, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(propertyWriter).serializeAsField(testPojo, jsonGenerator, serializerProvider);
        verifyNoInteractions(awsService);
        verify(jsonGenerator, never()).writeStringField(anyString(), anyString());
    }

    @Test
    void serializeAsField_whenImageUrlIsNull_shouldCallWriterSerializeAsField() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        Object testPojo = new Object(); // Object without getImageUrl method

        // Act
        serializer.serializeAsField(testPojo, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(propertyWriter).serializeAsField(testPojo, jsonGenerator, serializerProvider);
        verifyNoInteractions(awsService);
        verify(jsonGenerator, never()).writeStringField(anyString(), anyString());
    }

    @Test
    void serializeAsField_whenImageUrlStartsWithHttps_shouldWriteUrlDirectly() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        FractionIcon fractionIcon = new FractionIcon();
        fractionIcon.setImageUrl("https://example.com/image.png");

        // Act
        serializer.serializeAsField(fractionIcon, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(jsonGenerator).writeStringField("image_url", "https://example.com/image.png");
        verifyNoInteractions(awsService);
        verify(propertyWriter, never()).serializeAsField(any(), any(), any());
    }

    @Test
    void serializeAsField_whenImageUrlDoesNotStartWithHttps_shouldGeneratePresignedUrl() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        when(presignedUrlResponse.getPreSignedUrl()).thenReturn("https://presigned-url.com/image.png");
        when(awsService.generatePresignedDownloadUrl("relative/path/image.png", Duration.ofMinutes(15))).thenReturn(
                presignedUrlResponse);

        FractionIcon fractionIcon = new FractionIcon();
        fractionIcon.setImageUrl("relative/path/image.png");

        // Act
        serializer.serializeAsField(fractionIcon, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(awsService).generatePresignedDownloadUrl("relative/path/image.png", Duration.ofMinutes(15));
        verify(jsonGenerator).writeStringField("image_url", "https://presigned-url.com/image.png");
        verify(propertyWriter, never()).serializeAsField(any(), any(), any());
    }

    // --- getImageUrlFromObject method tests (via serializeAsField) ---

    @Test
    void serializeAsField_withNullPojo_shouldCallWriterSerializeAsField() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");

        // Act
        serializer.serializeAsField(null, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(propertyWriter).serializeAsField(null, jsonGenerator, serializerProvider);
        verifyNoInteractions(awsService);
    }

    @Test
    void serializeAsField_withFractionIconEntity_shouldExtractImageUrl() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        FractionIcon fractionIcon = new FractionIcon();
        fractionIcon.setImageUrl("https://example.com/fraction-icon.png");

        // Act
        serializer.serializeAsField(fractionIcon, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(jsonGenerator).writeStringField("image_url", "https://example.com/fraction-icon.png");
    }

    @Test
    void serializeAsField_withFractionIconNestedDto_shouldExtractImageUrl() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        FractionIconNestedDto dto = new FractionIconNestedDto();
        dto.setImageUrl("https://example.com/nested-icon.png");

        // Act
        serializer.serializeAsField(dto, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(jsonGenerator).writeStringField("image_url", "https://example.com/nested-icon.png");
    }

    @Test
    void serializeAsField_withFractionIconDetailDto_shouldExtractImageUrl() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        FractionIconDetailDto dto = new FractionIconDetailDto();
        dto.setImageUrl("https://example.com/detail-icon.png");

        // Act
        serializer.serializeAsField(dto, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(jsonGenerator).writeStringField("image_url", "https://example.com/detail-icon.png");
    }

    @Test
    void serializeAsField_withObjectHavingGetImageUrlMethod_shouldUseReflection() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        TestObjectWithImageUrl testObject = new TestObjectWithImageUrl("https://example.com/reflection-icon.png");

        // Act
        serializer.serializeAsField(testObject, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(jsonGenerator).writeStringField("image_url", "https://example.com/reflection-icon.png");
    }

    @Test
    void serializeAsField_withObjectHavingGetImageUrlReturningNonString_shouldCallWriterSerializeAsField() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        TestObjectWithNonStringImageUrl testObject = new TestObjectWithNonStringImageUrl();

        // Act
        serializer.serializeAsField(testObject, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(propertyWriter).serializeAsField(testObject, jsonGenerator, serializerProvider);
        verifyNoInteractions(awsService);
    }

    @Test
    void serializeAsField_withObjectWithoutGetImageUrlMethod_shouldCallWriterSerializeAsField() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        TestObjectWithoutImageUrl testObject = new TestObjectWithoutImageUrl();

        // Act
        serializer.serializeAsField(testObject, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(propertyWriter).serializeAsField(testObject, jsonGenerator, serializerProvider);
        verifyNoInteractions(awsService);
    }

    @Test
    void serializeAsField_withObjectThrowingReflectiveOperationException_shouldCallWriterSerializeAsField() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        TestObjectThrowingException testObject = new TestObjectThrowingException();

        // Act
        serializer.serializeAsField(testObject, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(propertyWriter).serializeAsField(testObject, jsonGenerator, serializerProvider);
        verifyNoInteractions(awsService);
    }

    // --- Test helper classes ---

    /**
     * Test class that has a getImageUrl() method returning a String.
     */
    @Getter
    public static class TestObjectWithImageUrl {

        private final String imageUrl;

        public TestObjectWithImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

    }

    /**
     * Test class that has a getImageUrl() method returning a non-String.
     */
    public static class TestObjectWithNonStringImageUrl {

        public Integer getImageUrl() {
            return 123;
        }
    }

    /**
     * Test class that doesn't have a getImageUrl() method.
     */
    public static class TestObjectWithoutImageUrl {

        public String getSomeOtherField() {
            return "other";
        }
    }

    /**
     * Test class that throws an exception when getImageUrl() is called.
     */
    public static class TestObjectThrowingException {

        public String getImageUrl() throws Exception {
            throw new RuntimeException("Test exception");
        }
    }
}
