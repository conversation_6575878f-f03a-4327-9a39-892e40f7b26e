package de.interzero.oneepr.customer.shopping_cart;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import de.interzero.oneepr.customer.shopping_cart.dto.CreateShoppingCartDto;
import de.interzero.oneepr.customer.shopping_cart.dto.CreateShoppingCartItemDto;
import de.interzero.oneepr.customer.shopping_cart.dto.UpdateShoppingCartDto;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.jwt;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;


@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class ShoppingCartControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ShoppingCartRepository shoppingCartRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private EntityManager em;

    @MockBean
    JwtDecoder jwtDecoder;

    @Autowired
    private ShoppingCartItemRepository shoppingCartItemRepository;


    @BeforeEach
    void setup() {
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    private CreateShoppingCartItemDto buildItem() {
        CreateShoppingCartItemDto dto = new CreateShoppingCartItemDto();
        dto.setCountryId(1);
        dto.setCountryCode("DE");
        dto.setCountryName("Germany");
        dto.setCountryFlag("DE-flag");
        dto.setYear(2025);
        dto.setServiceType(Contract.Type.DIRECT_LICENSE);
        return dto;
    }

    /**
     * Integration test for POST /shopping-cart (create)
     */
    @Test
    void create_shouldCreateShoppingCart() throws Exception {
        CreateShoppingCartDto dto = new CreateShoppingCartDto();
        dto.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        dto.setJourneyStep("SHOPPING_CART");
        dto.setForce(false);

        String json = objectMapper.writeValueAsString(dto);

        var result = mockMvc.perform(post(Api.SHOPPING_CART).contentType(MediaType.APPLICATION_JSON).content(json))
                .andDo(print())
                .andExpect(status().isCreated())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.journey").value("DIRECT_LICENSE"))
                .andExpect(jsonPath("$.status").value("OPEN"))
                .andExpect(jsonPath("$.subtotal").value(0))
                .andExpect(jsonPath("$.total").value(0))
                .andReturn();

        String response = result.getResponse().getContentAsString();
        ShoppingCart created = objectMapper.readValue(response, ShoppingCart.class);

        assertThat(created.getId()).isNotNull();
        assertThat(created.getJourney()).isEqualTo(ShoppingCart.Journey.DIRECT_LICENSE);
        assertThat(created.getStatus()).isEqualTo(ShoppingCart.Status.OPEN);
        assertThat(created.getSubtotal()).isZero();
        assertThat(created.getTotal()).isZero();

        var fromDb = shoppingCartRepository.findById(created.getId());
        assertThat(fromDb).isPresent();
        assertThat(fromDb.get().getJourney()).isEqualTo(ShoppingCart.Journey.DIRECT_LICENSE);
    }

    /**
     * Negative case: a journey is required -> expect 400 BAD_REQUEST
     */
    @Test
    void create_shouldFail_whenJourneyMissing() throws Exception {
        CreateShoppingCartDto dto = new CreateShoppingCartDto();
        dto.setJourneyStep("SHOPPING_CART");
        dto.setForce(false);

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(post(Api.SHOPPING_CART).contentType(MediaType.APPLICATION_JSON).content(json))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    /**
     * Integration test for GET /shopping-cart/{id} (findOne) - success
     */
    @Test
    void findOne_shouldReturnShoppingCart_whenOpenAndNotDeleted() throws Exception {
        ShoppingCart cart = new ShoppingCart();
        cart.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        cart.setStatus(ShoppingCart.Status.OPEN);
        cart.setSubtotal(0);
        cart.setTotal(0);
        cart.setVatPercentage(0);
        cart.setVatValue(0);
        cart.setCouponValue(0);
        ShoppingCart saved = shoppingCartRepository.save(cart);

        assertThat(saved.getId()).isNotNull();

        mockMvc.perform(get(Api.SHOPPING_CART + "/" + saved.getId()).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(saved.getId()))
                .andExpect(jsonPath("$.status").value("OPEN"))
                .andExpect(jsonPath("$.journey").value("DIRECT_LICENSE"))
                .andExpect(jsonPath("$.subtotal").value(0))
                .andExpect(jsonPath("$.total").value(0));
    }

    /**
     * Integration test for GET /shopping-cart/{id} (findOne) - not found
     */
    @Test
    void findOne_shouldReturn404_whenCartDoesNotExist() throws Exception {
        mockMvc.perform(get(Api.SHOPPING_CART + "/00000000-0000-0000-0000-000000000000").accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isNotFound());
    }

    /**
     * Integration test for the GET /shopping-cart/{id} endpoint (findOne), specifically validating
     * the behavior when a shopping cart has been marked as deleted.
     *
     * @throws Exception if an error occurs during request execution or assertion.
     */
    @Test
    void findOne_shouldReturn404_whenCartDeleted() throws Exception {
        ShoppingCart cart = new ShoppingCart();
        cart.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        cart.setStatus(ShoppingCart.Status.OPEN);
        cart.setSubtotal(0);
        cart.setTotal(0);
        cart.setVatPercentage(0);
        cart.setVatValue(0);
        cart.setCouponValue(0);
        cart = shoppingCartRepository.save(cart);

        cart.setDeletedAt(LocalDate.now());
        shoppingCartRepository.save(cart);

        mockMvc.perform(get(Api.SHOPPING_CART + "/" + cart.getId()).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isNotFound());
    }

    /**
     * Integration test for the `GET /shopping-cart/email/{email}` endpoint.
     * <p>
     * Validates that a customer can successfully retrieve their own shopping cart
     * associated with their email address. The test sets up a shopping cart for the
     * given email and ensures that the response contains the correct email in the
     * returned JSON payload.
     *
     * @throws Exception if an error occurs during request execution or assertion
     */
    @Test
    void findOneByEmail_shouldReturnCart_forCustomerOwnEmail() throws Exception {
        String email = "<EMAIL>";

        ShoppingCart cart = new ShoppingCart();
        cart.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        cart.setStatus(ShoppingCart.Status.OPEN);
        cart.setEmail(email);
        cart.setSubtotal(0);
        cart.setTotal(0);
        cart.setVatPercentage(0);
        cart.setVatValue(0);
        cart.setCouponValue(0);
        shoppingCartRepository.save(cart);

        mockMvc.perform(get(Api.SHOPPING_CART + "/email/{email}", email).with(jwt().jwt(j -> j.claim("email", email))
                                                                                      .authorities(new SimpleGrantedAuthority(
                                                                                              "ROLE_CUSTOMER")))
                                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value(email));
    }

    /**
     * Tests the behavior of the `GET /shopping-cart/email/{email}` endpoint when a customer
     * attempts to retrieve a shopping cart that is associated with an email address
     * different from their own.
     *
     * @throws Exception if an error occurs during request execution or assertion
     */
    @Test
    @WithMockUser(
            username = "<EMAIL>",
            roles = {TestRole.CUSTOMER}
    )
    void findOneByEmail_shouldReturn403_forCustomerOtherEmail() throws Exception {
        ShoppingCart cart = new ShoppingCart();
        cart.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        cart.setStatus(ShoppingCart.Status.OPEN);
        cart.setEmail("<EMAIL>");
        cart.setSubtotal(0);
        cart.setTotal(0);
        cart.setVatPercentage(0);
        cart.setVatValue(0);
        cart.setCouponValue(0);
        shoppingCartRepository.save(cart);

        mockMvc.perform(get(
                        Api.SHOPPING_CART + "/email/{email}",
                        "<EMAIL>").accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isForbidden());
    }

    /**
     * Integration test for the `GET /shopping-cart/email/{email}` endpoint.
     *
     * @throws Exception if an error occurs during request execution or assertion
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOneByEmail_shouldReturnCart_forAdminAnyEmail() throws Exception {
        String email = "<EMAIL>";

        ShoppingCart cart = new ShoppingCart();
        cart.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        cart.setStatus(ShoppingCart.Status.OPEN);
        cart.setEmail(email);
        cart.setSubtotal(0);
        cart.setTotal(0);
        cart.setVatPercentage(0);
        cart.setVatValue(0);
        cart.setCouponValue(0);
        shoppingCartRepository.save(cart);

        mockMvc.perform(get(Api.SHOPPING_CART + "/email/{email}", email).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value(email));
    }

    /**
     * Tests the behavior of the `GET /shopping-cart/email/{email}` endpoint when an administrator user
     * attempts to retrieve a shopping cart by email, but no cart is associated with the given email.
     *
     * @throws Exception if an error occurs during request execution or assertion
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOneByEmail_shouldReturn404_whenCartMissing() throws Exception {
        mockMvc.perform(get(
                        Api.SHOPPING_CART + "/email/{email}",
                        "<EMAIL>").accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isNotFound());
    }

    /**
     * Tests the behavior of the `GET /shopping-cart/email/{email}` endpoint when an administrator user
     * attempts to retrieve a shopping cart by email, but the associated cart has been marked as deleted.
     * <p>
     * Assertions:
     * - Verifies that the response status is 404 (Not Found).
     *
     * @throws Exception if an error occurs during request execution or assertion
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOneByEmail_shouldReturn404_whenCartDeleted() throws Exception {
        ShoppingCart cart = new ShoppingCart();
        cart.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        cart.setStatus(ShoppingCart.Status.OPEN);
        cart.setEmail("<EMAIL>");
        cart.setSubtotal(0);
        cart.setTotal(0);
        cart.setVatPercentage(0);
        cart.setVatValue(0);
        cart.setCouponValue(0);
        cart = shoppingCartRepository.save(cart);

        cart.setDeletedAt(LocalDate.now());
        shoppingCartRepository.save(cart);

        mockMvc.perform(get(
                        Api.SHOPPING_CART + "/email/{email}",
                        "<EMAIL>").accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isNotFound());
    }

    /**
     * Integration test for the `GET /shopping-cart/purchased/{email}` endpoint.
     * <p>
     * A mocked authenticated user context is used to simulate a customer.
     *
     * @throws Exception if an error occurs during request execution or assertion
     */
    @Test
    @WithMockUser(
            username = "<EMAIL>",
            roles = {TestRole.CUSTOMER}
    )
    @SuppressWarnings("java:S2925")
    void findLastPurchasedByEmail_shouldReturnLast_forCustomerOwnEmail() throws Exception {
        String email = "<EMAIL>";

        ShoppingCart older = new ShoppingCart();
        older.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        older.setStatus(ShoppingCart.Status.PURCHASED);
        older.setEmail(email);
        older.setSubtotal(0);
        older.setTotal(0);
        older.setVatPercentage(0);
        older.setVatValue(0);
        older.setCouponValue(0);
        shoppingCartRepository.save(older);

        // Ensure created_at differs
        em.flush();                // push INSERT to DB
        Thread.sleep(25);

        ShoppingCart newer = new ShoppingCart();
        newer.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        newer.setStatus(ShoppingCart.Status.PURCHASED);
        newer.setEmail(email);
        newer.setSubtotal(0);
        newer.setTotal(0);
        newer.setVatPercentage(0);
        newer.setVatValue(0);
        newer.setCouponValue(0);
        newer = shoppingCartRepository.save(newer);

        try (MockedStatic<AuthUtil> mocked = Mockito.mockStatic(AuthUtil.class)) {
            AuthenticatedUser au = new AuthenticatedUser(newer.getId(), Role.CUSTOMER, email);
            mocked.when(AuthUtil::getRelevantUserDetails).thenReturn(au);

            mockMvc.perform(get(Api.SHOPPING_CART + "/purchased/{email}", email).accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.email").value(email))
                    .andExpect(jsonPath("$.id").value(newer.getId()))
                    .andExpect(jsonPath("$.status").value("PURCHASED"))
                    .andReturn();
        }
    }

    /**
     * Integration test for the `GET /shopping-cart/purchased/{email}` endpoint.
     * <p>
     * Uses a mocked authenticated context to simulate an administrator user
     * and prepares the required test data within the database using the
     * shoppingCartRepository.
     *
     * @throws Exception if an error occurs during request execution or assertion
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    @SuppressWarnings("java:S2925")
    void findLastPurchasedByEmail_shouldReturnLast_forAdminAnyEmail() throws Exception {
        String email = "<EMAIL>";

        ShoppingCart older = new ShoppingCart();
        older.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        older.setStatus(ShoppingCart.Status.PURCHASED);
        older.setEmail(email);
        older.setSubtotal(0);
        older.setTotal(0);
        older.setVatPercentage(0);
        older.setVatValue(0);
        older.setCouponValue(0);
        shoppingCartRepository.save(older);

        // Ensure created_at differs
        em.flush();                // push INSERT to DB
        Thread.sleep(25);

        ShoppingCart newer = new ShoppingCart();
        newer.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        newer.setStatus(ShoppingCart.Status.PURCHASED);
        newer.setEmail(email);
        newer.setSubtotal(0);
        newer.setTotal(0);
        newer.setVatPercentage(0);
        newer.setVatValue(0);
        newer.setCouponValue(0);
        newer = shoppingCartRepository.save(newer);

        try (MockedStatic<AuthUtil> mocked = Mockito.mockStatic(AuthUtil.class)) {
            AuthenticatedUser au = new AuthenticatedUser(newer.getId(), Role.CUSTOMER, email);
            mocked.when(AuthUtil::getRelevantUserDetails).thenReturn(au);

            mockMvc.perform(get(Api.SHOPPING_CART + "/purchased/{email}", email).accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.email").value(email))
                    .andExpect(jsonPath("$.id").value(newer.getId()))
                    .andExpect(jsonPath("$.status").value("PURCHASED"))
                    .andReturn();
        }
    }

    /**
     * Integration test for PUT /shopping-cart/{id} (update).
     *
     * @throws Exception if an error occurs during the mock request execution
     *                   or assertions.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void update_shouldUpdateCart_whenNoEmail_set() throws Exception {
        ShoppingCart cart = new ShoppingCart();
        cart.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        cart.setStatus(ShoppingCart.Status.OPEN);
        cart.setSubtotal(0);
        cart.setTotal(0);
        cart.setVatPercentage(0);
        cart.setVatValue(0);
        cart.setCouponValue(0);
        cart = shoppingCartRepository.save(cart);

        UpdateShoppingCartDto dto = new UpdateShoppingCartDto();
        dto.setJourneyStep("CHECKOUT");
        dto.setVatPercentage(7);

        String json = objectMapper.writeValueAsString(dto);

        var result = mockMvc.perform(put(Api.SHOPPING_CART + "/{id}", cart.getId()).with(csrf())
                                             .contentType(MediaType.APPLICATION_JSON)
                                             .content(json)
                                             .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(cart.getId()))
                .andExpect(jsonPath("$.journey_step").value("CHECKOUT"))
                .andExpect(jsonPath("$.vat_percentage").value(7))
                .andReturn();

        ShoppingCart updated = objectMapper.readValue(result.getResponse().getContentAsString(), ShoppingCart.class);
        assertThat(updated.getJourneyStep()).isEqualTo("CHECKOUT");
        assertThat(updated.getVatPercentage()).isEqualTo(7);

        var fromDb = shoppingCartRepository.findById(updated.getId());
        assertThat(fromDb).isPresent();
        assertThat(fromDb.get().getJourneyStep()).isEqualTo("CHECKOUT");
        assertThat(fromDb.get().getVatPercentage()).isEqualTo(7);
    }

    /**
     * Integration test for the `PUT /shopping-cart/{id}` endpoint.
     * <p>
     * Verifies that an authenticated user with the role of CUSTOMER is unable to update
     * a shopping cart when the cart's email address does not match the user's email address.
     *
     * @throws Exception if an error occurs during the mock request execution or assertions.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void update_shouldReturn403_forCustomerDifferentEmail() throws Exception {
        ShoppingCart cart = new ShoppingCart();
        cart.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        cart.setStatus(ShoppingCart.Status.OPEN);
        cart.setEmail("<EMAIL>");
        cart.setSubtotal(0);
        cart.setTotal(0);
        cart.setVatPercentage(0);
        cart.setVatValue(0);
        cart.setCouponValue(0);
        cart = shoppingCartRepository.save(cart);

        UpdateShoppingCartDto dto = new UpdateShoppingCartDto();
        dto.setJourneyStep("CHECKOUT");
        String json = objectMapper.writeValueAsString(dto);

        try (MockedStatic<AuthUtil> mocked = Mockito.mockStatic(AuthUtil.class)) {
            AuthenticatedUser au = new AuthenticatedUser("any-id", Role.CUSTOMER, "<EMAIL>");
            mocked.when(AuthUtil::getRelevantUserDetails).thenReturn(au);

            mockMvc.perform(put(Api.SHOPPING_CART + "/{id}", cart.getId()).with(csrf())
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(json)
                                    .accept(MediaType.APPLICATION_JSON))
                    .andDo(print())
                    .andExpect(status().isForbidden());
        }
    }

    @Test
    @WithMockUser(roles = {"CUSTOMER"})
    void addItem_shouldReturn403_forCustomerWithDifferentEmail() throws Exception {
        ShoppingCart cart = new ShoppingCart();
        cart.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        cart.setStatus(ShoppingCart.Status.OPEN);
        cart.setEmail("<EMAIL>");
        cart.setSubtotal(0);
        cart.setTotal(0);
        cart.setVatPercentage(0);
        cart.setVatValue(0);
        cart.setCouponValue(0);
        cart = shoppingCartRepository.save(cart);

        CreateShoppingCartItemDto body = buildItem();
        String json = objectMapper.writeValueAsString(body);

        try (MockedStatic<AuthUtil> mocked = Mockito.mockStatic(AuthUtil.class)) {
            AuthenticatedUser au = new AuthenticatedUser("any-id", Role.CUSTOMER, "<EMAIL>");
            mocked.when(AuthUtil::getRelevantUserDetails).thenReturn(au);

            mockMvc.perform(post(Api.SHOPPING_CART + "/{id}/items", cart.getId()).with(csrf())
                                    .contentType(MediaType.APPLICATION_JSON)
                                    .content(json)
                                    .accept(MediaType.APPLICATION_JSON))
                    .andDo(print())
                    .andExpect(status().isForbidden());
        }
    }

    /**
     * Integration test for the `DELETE /shopping-cart/{id}/items/{item_id}` endpoint.
     *
     * @throws Exception if an error occurs during the request execution or assertions.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void removeItem_shouldDelete_andReturnUpdatedCart() throws Exception {
        ShoppingCart cart = new ShoppingCart();
        cart.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        cart.setStatus(ShoppingCart.Status.OPEN);
        cart.setSubtotal(0);
        cart.setTotal(0);
        cart.setVatPercentage(0);
        cart.setVatValue(0);
        cart.setCouponValue(0);
        cart.setEmail(null);
        cart = shoppingCartRepository.save(cart);

        ShoppingCartItem item = new ShoppingCartItem();
        item.setShoppingCart(cart);
        item.setCountryId(1);
        item.setCountryCode("DE");
        item.setCountryName("Germany");
        item.setCountryFlag("DE");
        item.setYear(2025);
        item.setServiceType(Contract.Type.DIRECT_LICENSE);
        item.setPriceList(Map.of());
        item.setPackagingServices(null);
        item.setSpecificationType(ShoppingCartItem.SpecificationType.PURCHASE);
        item.setPrice(0);

        item = shoppingCartItemRepository.save(item);

        mockMvc.perform(delete(Api.SHOPPING_CART + "/{id}/items/{item_id}", cart.getId(), item.getId()))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(cart.getId()))
                .andExpect(jsonPath("$.items.length()").value(0));
    }
}
