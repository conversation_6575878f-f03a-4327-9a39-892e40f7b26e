export interface Criteria {
  id: number;
  type: CriteriaType;
  mode: CriteriaMode;
  title: string | null;
  help_text: string | null;
  input_type: CriteriaInputType | null;
  calculator_type: CriteriaCalculatorType | null;
  country_id: number;
  packaging_service_id: number | null;
  required_information_id: number | null;
  obligation_check_section_id?: number | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  options: CriteriaOption[];
}

export interface CriteriaOption {
  id: number;
  option_value: string;
  option_to_value: string | null;
  value: string;
  conditional_criteria_id?: number | null; // Reference to conditional question
  packaging_service_ids?: number[]; // For obligation results with specific services
  packaging_services?: { id: number; name: string }[]; // For obligation results with service details
}

export type CriteriaMode = "COMMITMENT" | "CALCULATOR";

export type CriteriaType =
  | "PACKAGING_SERVICE"
  | "REPORT_SET"
  | "REPORT_FREQUENCY"
  | "OTHER_COST"
  | "PRICE_LIST"
  | "REQUIRED_INFORMATION";

export type CriteriaInputType = "SELECT" | "YES_NO";

export type CriteriaCalculatorType = "LICENSE_FEES" | "TOTAL_IN_TONS" | "TOTAL_IN_KG";

export type PriceListConditionType = "LICENSE_YEAR";

export type CreateCriteriaOption = Omit<CriteriaOption, "id"> & {
  id?: number;
};

export type CreateCriteria = Omit<Criteria, "id" | "created_at" | "updated_at" | "deleted_at" | "options"> & {
  options?: CreateCriteriaOption[];
};

export type UpdateCriteria = Partial<
  Omit<Criteria, "id" | "created_at" | "updated_at" | "deleted_at" | "options"> & {
    options?: CreateCriteriaOption[];
  }
>;
