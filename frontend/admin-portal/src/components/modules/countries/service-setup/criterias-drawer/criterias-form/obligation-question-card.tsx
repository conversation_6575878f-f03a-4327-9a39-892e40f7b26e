import { useState } from "react";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Chip } from "@/components/ui/chip";
import { ChevronDown, ChevronUp } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { useMutation } from "@tanstack/react-query";
import { deleteCriteria } from "@/lib/api/criterias";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { enqueueSnackbar } from "notistack";
import { invalidateSubQueries } from "./index";
import { queryClient } from "@/lib/react-query";
import { CharacterCounter } from "@/components/ui/character-counter";
import { Skeleton } from "@/components/ui/skeleton";
import { useFormContext, useWatch } from "react-hook-form";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  ObligationCheckFormData,
  ObligationQuestion,
  TITLE_MAX_LENGTH,
  HELP_TEXT_MAX_LENGTH,
} from "./obligation-check-form";

interface ObligationQuestionCardProps {
  sectionId: string;
  sectionIndex: number;
  questionIndex: number;
  question: ObligationQuestion;
  questionNumber: number;
  availableQuestions: { id?: number; title: string; displayNumber: number }[];
  availableServices: { id: number; name: string }[];
  packagingServiceId?: number;
  requiredInformationId?: number;
  onUpdate: (sectionId: string, questionIndex: number, data: Partial<ObligationQuestion>) => void;
  onRemove: (sectionId: string, questionIndex: number) => void;
}

interface AnswerBranchProps {
  answerType: "yes" | "no";
  action: "show_conditional" | "indicates_obligation" | "does_not_indicate_obligation" | undefined;
  targetQuestionId?: number;
  services?: string[];
  onActionChange: (action: string) => void;
  onTargetQuestionIdChange: (value: number) => void;
  onServiceAdd: (serviceId: string) => void;
  onServiceRemove: (serviceName: string) => void;
  availableQuestions: { id?: number; title: string; displayNumber: number }[];
  availableServices: { id: number; name: string }[];
  questionNumber: number;
}

const ACTION_OPTIONS = [
  { value: "indicates_obligation", label: "Indicates obligation" },
  { value: "does_not_indicate_obligation", label: "Does not indicate obligation" },
  { value: "show_conditional", label: "Show conditional" },
];

// Result selection removed per UX: indicates_obligation always implies OBLIGED

const AnswerBranch = ({
  answerType,
  action,
  targetQuestionId,
  services = [],
  onActionChange,
  onTargetQuestionIdChange,
  onServiceAdd,
  onServiceRemove,
  availableQuestions,
  availableServices,
}: AnswerBranchProps) => {
  return (
    <div className="flex items-start gap-4">
      <div className="w-32 h-12 bg-tonal-dark-cream-96 rounded-lg text-primary px-3 py-4">
        {answerType === "yes" ? "Yes" : "No"}
      </div>
      <p className="text-tonal-dark-green-30 pt-4">Then</p>

      <div className="w-full md:w-[480px]">
        <Select value={action || ""} onValueChange={onActionChange}>
          <SelectTrigger aria-label="Select action">
            <SelectValue placeholder="Select action" />
          </SelectTrigger>
          <SelectContent>
            {ACTION_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {action === "show_conditional" && (
        <div className="w-full md:w-[480px]">
          <Select
            value={targetQuestionId?.toString() || ""}
            onValueChange={(value) => onTargetQuestionIdChange(parseInt(value))}
          >
            <SelectTrigger
              aria-label="Select question"
              invalid={Boolean(
                action === "show_conditional" &&
                  (!targetQuestionId || !availableQuestions.find((q) => q.id === targetQuestionId))
              )}
            >
              <SelectValue placeholder="Select question" />
            </SelectTrigger>
            <SelectContent>
              {availableQuestions.map((q) => {
                const isCurrentQuestion = q.id === undefined;
                return (
                  <SelectItem
                    key={q.displayNumber}
                    value={(q.id || 0).toString()}
                    disabled={isCurrentQuestion || q.id === undefined}
                    className={isCurrentQuestion ? "text-gray-400 cursor-not-allowed" : ""}
                  >
                    Question {q.displayNumber.toString().padStart(2, "0")}
                    {isCurrentQuestion && " (current question)"}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>

          {targetQuestionId && !availableQuestions.find((q) => q.id === targetQuestionId) && (
            <div className="flex items-center gap-2 mt-2 text-red-500 text-sm">
              <span className="text-red-500">⚠</span>
              <span>Conditional questions must be in the same section.</span>
            </div>
          )}
        </div>
      )}

      {action === "does_not_indicate_obligation" && (
        <div className="text-tonal-dark-cream-40 text-sm">
          Customer is not obliged (no further configuration needed)
        </div>
      )}

      {action === "indicates_obligation" && (
        <>
          <p className="text-tonal-dark-green-30 pt-4">For</p>
          <div className="w-full md:w-[720px] min-w-0">
            <Select>
              <SelectTrigger className="min-h-[56px] items-start" aria-label="Select service types">
                {services.length === 0 ? (
                  <span className="text-tonal-dark-cream-40">Select service types</span>
                ) : (
                  <div className="flex flex-col items-start gap-2 py-1 w-full min-w-0">
                    {services.slice(0, 2).map((serviceName) => (
                      <Chip key={serviceName} size="md" variant="beige" fullWidth aria-label={serviceName}>
                        <span className="block w-full overflow-hidden text-ellipsis whitespace-nowrap">
                          EPR Compliance Packaging - {serviceName}
                        </span>
                      </Chip>
                    ))}
                    {services.length > 2 && (
                      <Chip
                        size="sm"
                        variant="beige"
                        className="w-auto max-w-max shrink-0 self-start inline-flex px-3 py-1.5"
                      >
                        +{services.length - 2}
                      </Chip>
                    )}
                  </div>
                )}
              </SelectTrigger>
              <SelectContent>
                <div className="p-2">
                  <div className="space-y-2">
                    {availableServices.map((service) => (
                      <label
                        key={service.id}
                        className="flex items-center gap-2 cursor-pointer p-2 hover:bg-gray-50 rounded"
                      >
                        <input
                          type="checkbox"
                          checked={services.includes(service.name)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              onServiceAdd(service.id.toString());
                            } else {
                              onServiceRemove(service.name);
                            }
                          }}
                          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          aria-label={`Toggle service ${service.name}`}
                        />
                        <span className="text-sm text-primary">EPR Compliance Packaging - {service.name}</span>
                      </label>
                    ))}
                  </div>
                  {availableServices.length === 0 && (
                    <p className="text-sm text-tonal-dark-cream-40 p-2">No service types available</p>
                  )}
                </div>
              </SelectContent>
            </Select>
          </div>
        </>
      )}
    </div>
  );
};

export function ObligationQuestionCardSkeleton() {
  return (
    <div className="bg-background p-6 rounded-[20px]">
      <div className="flex items-center justify-between py-2">
        <Skeleton className="h-6 w-32 rounded" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-20 rounded" />
          <Skeleton className="h-8 w-8 rounded" />
        </div>
      </div>
      <div className="space-y-6 mt-4">
        <div className="grid grid-cols-1 gap-6">
          <Skeleton className="h-10 rounded" />
        </div>
        <Skeleton className="h-20 rounded" />
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Skeleton className="w-32 h-12 rounded-lg" />
            <Skeleton className="w-8 h-4 rounded" />
            <Skeleton className="w-full h-10 rounded" />
          </div>
          <div className="flex items-center gap-4">
            <Skeleton className="w-32 h-12 rounded-lg" />
            <Skeleton className="w-8 h-4 rounded" />
            <Skeleton className="w-full h-10 rounded" />
          </div>
        </div>
      </div>
    </div>
  );
}

export function ObligationQuestionCard({
  sectionId,
  sectionIndex,
  questionIndex,
  question,
  questionNumber,
  availableQuestions,
  availableServices,
  packagingServiceId,
  requiredInformationId,
  onUpdate,
  onRemove,
}: ObligationQuestionCardProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const { country } = useServiceSetup();

  const {
    register,
    control,
    setValue,
    formState: { errors },
  } = useFormContext<ObligationCheckFormData>();

  // Use useWatch to monitor form state changes
  const titleValue = useWatch({
    control,
    name: `sections.${sectionIndex}.questions.${questionIndex}.title`,
    defaultValue: question.title || "",
  });

  const helpTextValue = useWatch({
    control,
    name: `sections.${sectionIndex}.questions.${questionIndex}.help_text`,
    defaultValue: question.help_text || "",
  });

  const yesAction = useWatch({
    control,
    name: `sections.${sectionIndex}.questions.${questionIndex}.yes_action`,
    defaultValue: question.yes_action,
  });

  const noAction = useWatch({
    control,
    name: `sections.${sectionIndex}.questions.${questionIndex}.no_action`,
    defaultValue: question.no_action,
  });

  const yesTargetQuestionId = useWatch({
    control,
    name: `sections.${sectionIndex}.questions.${questionIndex}.yes_target_question_id`,
    defaultValue: question.yes_target_question_id,
  });

  const noTargetQuestionId = useWatch({
    control,
    name: `sections.${sectionIndex}.questions.${questionIndex}.no_target_question_id`,
    defaultValue: question.no_target_question_id,
  });

  const yesServices = useWatch({
    control,
    name: `sections.${sectionIndex}.questions.${questionIndex}.yes_services`,
    defaultValue: question.yes_services || [],
  });

  const noServices = useWatch({
    control,
    name: `sections.${sectionIndex}.questions.${questionIndex}.no_services`,
    defaultValue: question.no_services || [],
  });

  const { mutate: removeCriteria, isPending: isDeletingCriteria } = useMutation({
    mutationFn: (criteriaId: number) => deleteCriteria(criteriaId),
  });

  const handleRemoveQuestion = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (question.id) {
      e.preventDefault();

      return removeCriteria(question.id, {
        onSuccess: () => {
          onRemove(sectionId, questionIndex);
          // Ensure sections + criteria are reloaded for obligation flow
          queryClient.invalidateQueries({ queryKey: ["obligation-check-sections", country.code] });
          invalidateSubQueries("PACKAGING_SERVICE", country.code, packagingServiceId, requiredInformationId);
          enqueueSnackbar("Question removed successfully", { variant: "success" });
        },
        onError: () => {
          enqueueSnackbar("Failed to remove question. Please try again.", { variant: "error" });
        },
      });
    }

    onRemove(sectionId, questionIndex);
    enqueueSnackbar("Question removed successfully", { variant: "success" });
  };

  const titleLength = titleValue?.length || 0;
  const helpTextLength = helpTextValue?.length || 0;

  // Action change handlers with form state management
  const handleYesActionChange = (action: string) => {
    setValue(
      `sections.${sectionIndex}.questions.${questionIndex}.yes_action`,
      action as "show_conditional" | "indicates_obligation" | "does_not_indicate_obligation",
      { shouldDirty: true }
    );
    setValue(`sections.${sectionIndex}.questions.${questionIndex}.yes_target_question_id`, undefined, {
      shouldDirty: true,
    });
    setValue(`sections.${sectionIndex}.questions.${questionIndex}.yes_obligation_result`, undefined, {
      shouldDirty: true,
    });
    setValue(`sections.${sectionIndex}.questions.${questionIndex}.yes_services`, [], { shouldDirty: true });
    // Also update parent component for immediate UI updates
    onUpdate(sectionId, questionIndex, {
      yes_action: action as "show_conditional" | "indicates_obligation" | "does_not_indicate_obligation",
      yes_target_question_id: undefined,
      yes_obligation_result: undefined,
      yes_services: [],
    });
  };

  const handleNoActionChange = (action: string) => {
    setValue(
      `sections.${sectionIndex}.questions.${questionIndex}.no_action`,
      action as "show_conditional" | "indicates_obligation" | "does_not_indicate_obligation",
      { shouldDirty: true }
    );
    setValue(`sections.${sectionIndex}.questions.${questionIndex}.no_target_question_id`, undefined, {
      shouldDirty: true,
    });
    setValue(`sections.${sectionIndex}.questions.${questionIndex}.no_obligation_result`, undefined, {
      shouldDirty: true,
    });
    setValue(`sections.${sectionIndex}.questions.${questionIndex}.no_services`, [], { shouldDirty: true });
    // Also update parent component for immediate UI updates
    onUpdate(sectionId, questionIndex, {
      no_action: action as "show_conditional" | "indicates_obligation" | "does_not_indicate_obligation",
      no_target_question_id: undefined,
      no_obligation_result: undefined,
      no_services: [],
    });
  };

  const handleServiceAdd = (serviceId: string, answerType: "yes" | "no") => {
    const service = availableServices.find((s) => s.id.toString() === serviceId);
    if (!service) return;

    const currentServices = answerType === "yes" ? yesServices || [] : noServices || [];
    if (!currentServices.includes(service.name)) {
      const updatedServices = [...currentServices, service.name];
      setValue(`sections.${sectionIndex}.questions.${questionIndex}.${answerType}_services`, updatedServices, {
        shouldDirty: true,
      });
      onUpdate(sectionId, questionIndex, {
        [answerType === "yes" ? "yes_services" : "no_services"]: updatedServices,
      });
    }
  };

  const handleServiceRemove = (serviceName: string, answerType: "yes" | "no") => {
    const currentServices = answerType === "yes" ? yesServices || [] : noServices || [];
    const updatedServices = currentServices.filter((s) => s !== serviceName);
    setValue(`sections.${sectionIndex}.questions.${questionIndex}.${answerType}_services`, updatedServices, {
      shouldDirty: true,
    });
    onUpdate(sectionId, questionIndex, {
      [answerType === "yes" ? "yes_services" : "no_services"]: updatedServices,
    });
  };

  return (
    <div className="bg-background p-6 rounded-[20px]">
      <div className="flex items-center justify-between py-2">
        <p className="text-tonal-dark-cream-10 font-bold text-xl">
          Question {questionNumber.toString().padStart(2, "0")}
        </p>
        <div className="flex items-center gap-2">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <div>
                <Button type="button" variant="text" color="red" size="small" aria-label="Remove question">
                  Delete
                </Button>
              </div>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>This action cannot be undone.</AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleRemoveQuestion} disabled={isDeletingCriteria}>
                  {isDeletingCriteria ? "Removing..." : "Remove"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
          <Button
            type="button"
            variant="text"
            color="light-blue"
            size="small"
            onClick={() => setIsExpanded(!isExpanded)}
            aria-expanded={isExpanded}
            aria-label={isExpanded ? "Collapse question" : "Expand question"}
          >
            {isExpanded ? <ChevronUp className="size-4" /> : <ChevronDown className="size-4" />}
          </Button>
        </div>
      </div>

      {isExpanded && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <div className="lg:col-span-2">
              <Input
                placeholder="Enter question"
                {...register(`sections.${sectionIndex}.questions.${questionIndex}.title`)}
                variant={errors.sections?.[sectionIndex]?.questions?.[questionIndex]?.title ? "error" : "default"}
                errorMessage={errors.sections?.[sectionIndex]?.questions?.[questionIndex]?.title?.message}
                maxLength={TITLE_MAX_LENGTH}
                aria-describedby={`question-title-character-counter-${sectionId}-${questionIndex}`}
              />
              <CharacterCounter
                className="mt-2"
                id={`question-title-character-counter-${sectionId}-${questionIndex}`}
                value={titleLength}
                max={TITLE_MAX_LENGTH}
              />
            </div>
          </div>

          <Textarea
            label="Help text (optional)"
            placeholder="Enter help text"
            {...register(`sections.${sectionIndex}.questions.${questionIndex}.help_text`)}
            rows={2}
            maxLength={HELP_TEXT_MAX_LENGTH}
            errorMessage={errors.sections?.[sectionIndex]?.questions?.[questionIndex]?.help_text?.message}
            aria-describedby={`help-text-character-counter-${sectionId}-${questionIndex}`}
          />
          <CharacterCounter
            className="mt-2"
            id={`help-text-character-counter-${sectionId}-${questionIndex}`}
            value={helpTextLength}
            max={HELP_TEXT_MAX_LENGTH}
          />

          <div className="my-6 space-y-8">
            <AnswerBranch
              answerType="yes"
              action={yesAction}
              targetQuestionId={yesTargetQuestionId}
              services={yesServices}
              onActionChange={handleYesActionChange}
              onTargetQuestionIdChange={(value) => {
                setValue(`sections.${sectionIndex}.questions.${questionIndex}.yes_target_question_id`, value, {
                  shouldDirty: true,
                });
                onUpdate(sectionId, questionIndex, { yes_target_question_id: value });
              }}
              onServiceAdd={(serviceId) => handleServiceAdd(serviceId, "yes")}
              onServiceRemove={(serviceName) => handleServiceRemove(serviceName, "yes")}
              availableQuestions={availableQuestions}
              availableServices={availableServices}
              questionNumber={questionNumber}
            />

            <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />

            <AnswerBranch
              answerType="no"
              action={noAction}
              targetQuestionId={noTargetQuestionId}
              services={noServices}
              onActionChange={handleNoActionChange}
              onTargetQuestionIdChange={(value) => {
                setValue(`sections.${sectionIndex}.questions.${questionIndex}.no_target_question_id`, value, {
                  shouldDirty: true,
                });
                onUpdate(sectionId, questionIndex, { no_target_question_id: value });
              }}
              onServiceAdd={(serviceId) => handleServiceAdd(serviceId, "no")}
              onServiceRemove={(serviceName) => handleServiceRemove(serviceName, "no")}
              availableQuestions={availableQuestions}
              availableServices={availableServices}
              questionNumber={questionNumber}
            />
          </div>
        </div>
      )}
    </div>
  );
}
