"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { getServiceSetupPackagingServices } from "@/lib/api/service-setups";
import { PackagingService } from "@/types/service-setup/packaging-service";
import { CheckCircle } from "@interzero/oneepr-react-ui/Icon";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { useQuery } from "@tanstack/react-query";
import { MoveRight } from "lucide-react";

export function ServiceSetupObligationCheck() {
  const { paramValues, changeParam } = useQueryFilter(["step"]);

  const isSelected = paramValues.step === "obligation-check";

  function handleOpenStep() {
    changeParam("step", "obligation-check");
  }

  const { country } = useServiceSetup();

  const { data: packagingServices, isFetching } = useQuery({
    queryKey: ["service-setup-packaging-services", country.code],
    queryFn: () => getServiceSetupPackagingServices(country.code),
  });

  const isComplete = packagingServices && !!packagingServices.length && packagingServices.every((p) => p.has_criteria);

  if (isFetching) {
    return (
      <div className="bg-background rounded-[20px] p-8 space-y-2" id="obligation-check-step">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
      </div>
    );
  }

  return (
    <div
      data-selected={isSelected}
      data-complete={isComplete}
      className="group bg-background rounded-[20px] p-8 cursor-pointer data-[selected=true]:cursor-default"
      onClick={() => !isSelected && handleOpenStep()}
      id="obligation-check-step"
    >
      <div className="flex items-center justify-between">
        <h3 className="text-primary text-2xl font-bold">2. Obligation check</h3>
        {!isSelected && isComplete && <CheckCircle className="size-6 fill-success transition-all duration-300" />}
      </div>

      {isSelected && <ObligationCheckForm packagingServices={packagingServices || []} />}
    </div>
  );
}

interface ObligationCheckFormProps {
  packagingServices: PackagingService[];
}

function ObligationCheckForm({ packagingServices }: ObligationCheckFormProps) {
  const { openCriteriasDrawer } = useServiceSetup();

  function handleSetupObligationCheck() {
    // If there are packaging services, open criteria drawer for the first service that doesn't have criteria
    const serviceWithoutCriteria = packagingServices.find((service) => !service.has_criteria);
    const targetService = serviceWithoutCriteria || packagingServices[0];

    if (targetService) {
      openCriteriasDrawer({
        type: "PACKAGING_SERVICE",
        packagingServiceId: targetService.id,
      });
    }
  }

  return (
    <div className="mt-6 space-y-6">
      <p className="text-primary">Define the obligation check form visible to the customer.</p>
      <div className="space-y-6">
        <div className="flex items-center justify-end">
          <Button
            type="button"
            onClick={handleSetupObligationCheck}
            variant="filled"
            color="yellow"
            size="medium"
            className="w-auto"
            trailingIcon={<MoveRight className="size-4" />}
          >
            Set up obligation check
          </Button>
        </div>
      </div>
    </div>
  );
}
