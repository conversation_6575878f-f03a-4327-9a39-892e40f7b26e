"use client";

import { FullReportSet } from "@/types/service-setup";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { useFormContext } from "react-hook-form";
import { ReportSetDelete } from "../components/report-set-delete";
import { ReportSetFormData } from "../components/report-set-form-provider";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { ReportSetFractions } from "../components/report-set-fractions";
import { ReportSetColumns } from "../components/report-set-columns";
import { Tabs, TabsList, TabsContent, TabsTrigger } from "@/components/ui/tabs";
import { ReportSetPriceLists } from "../components/report-set-price-lists";
import { Error } from "@interzero/oneepr-react-ui/Icon";
import { useMutationState } from "@tanstack/react-query";
import { useEffect } from "react";

interface ReportSetPlataformFormProps {
  countryCode: string;
  reportSet: FullReportSet;
  onFormChange?: (hasChanges: boolean) => void;
}

export function ReportSetPlataformForm({ reportSet, onFormChange }: ReportSetPlataformFormProps) {
  const {
    register,
    formState: { errors, isDirty },
  } = useFormContext<ReportSetFormData>();

  useEffect(() => {
    if (onFormChange) {
      onFormChange(isDirty);
    }
  }, [isDirty, onFormChange]);

  const updateReportSetMutation = useMutationState({
    filters: {
      mutationKey: ["update-report-set", reportSet.id],
      status: "pending",
    },
  });

  function getFormError() {
    const parsedErrors = Object.entries(errors || {});

    const firstError = parsedErrors[0];

    if (!firstError) return null;

    const [key, error] = firstError;

    if (key === "fractions") {
      if (error.root?.message) return `Fractions: ${error.root.message}`;

      if (Array.isArray(error)) return "Fractions: Error in some fraction input";

      return `Fractions: ${error?.message}`;
    }

    if (key === "columns") {
      if (Array.isArray(error)) return "Columns: Error in some column input";

      return `Columns: ${error?.message}`;
    }

    if (key === "price_lists") {
      return `Price Lists: ${error?.message}`;
    }

    return `General: ${error?.message}`;
  }

  const isSubmitting = !!updateReportSetMutation.length;

  return (
    <div className="mt-6 space-y-10">
      <div className="bg-background rounded-[20px] p-8 space-y-6">
        <div className="flex items-center justify-between">
          <p className="text-primary text-lg font-bold">New report set</p>
          <ReportSetDelete reportSetId={reportSet.id} />
        </div>
        <div className="w-full">
          <Input
            label="Fraction set name*"
            placeholder="Name for the Report Set"
            {...register("name")}
            variant={errors.name ? "error" : "default"}
            errorMessage={errors.name?.message}
          />
        </div>
      </div>
      <h3 className="text-primary text-3xl font-bold">1° Step: Set all fractions for this service</h3>
      <div className="bg-background rounded-[20px] py-6 px-5 space-y-8 w-full">
        <Tabs defaultValue="fractions" className="w-full">
          <TabsList className="pt-6 pb-4">
            <TabsTrigger value="fractions" className="bg-tonal-dark-blue-96">
              {errors.fractions && <Error className="fill-error size-4 mr-2" />}
              Fractions
            </TabsTrigger>
            <TabsTrigger value="price-lists" className="bg-tonal-dark-blue-96">
              {errors.price_lists && <Error className="fill-error size-4 mr-2" />}
              PRO pricing
            </TabsTrigger>
          </TabsList>
          <TabsContent value="fractions">
            <ReportSetFractions />
          </TabsContent>
          <TabsContent value="price-lists">
            <ReportSetPriceLists />
          </TabsContent>
        </Tabs>
      </div>
      <h3 className="text-primary text-3xl font-bold">2º Step: Configure columns</h3>
      {
        <div className="bg-background rounded-[20px] py-11 px-5 space-y-8 w-full">
          <ReportSetColumns />
        </div>
      }
      {!!getFormError() && (
        <div className="flex items-center gap-2 mb-2">
          <Error className="fill-error size-4" />
          <p className="text-error text-sm text-right">{getFormError()}</p>
        </div>
      )}
      <div className="w-full flex items-center justify-end">
        <Button
          type="submit"
          variant="filled"
          color={!!Object.keys(errors).length ? "red" : "yellow"}
          size="large"
          className="w-60"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Saving..." : "Save"}
        </Button>
      </div>
    </div>
  );
}
