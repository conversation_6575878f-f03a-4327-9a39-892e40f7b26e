export interface ServiceItemProps {
  position: number;
  label: string;
  countries: number;
  licenses: number;
}

export function ServiceItem({ position, label, countries, licenses }: ServiceItemProps) {
  return (
    <div className="flex flex-row justify-between items-center border-b p-2 border-tonal-dark-cream-80">
      <div className="flex gap-4 items-center">
        <span className="col-span-1 text-2xl text-[#808FA9] font-bold">{position}º</span>
        <div className="col-span-3 flex flex-col">
          <span className="text-tonal-dark-cream-10 max-w-36 break-normal">{label}</span>
          <span className="text-sm text-tonal-dark-cream-50">{licenses} licenses</span>
        </div>
      </div>
      <span className="text-xs text-[#808FA9] font-bold self-start">{countries} countries</span>
    </div>
  );
}
