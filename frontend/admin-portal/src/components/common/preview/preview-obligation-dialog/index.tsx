import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON>D<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { getObligationCheckSectionsByCountryCode, getServiceSetupPackagingServices } from "@/lib/api/service-setups";
import { getCriteriasBySectionId } from "@/lib/api/criterias";
import { useQuery } from "@tanstack/react-query";
import { AlertCircle } from "lucide-react";
import { ObligationCheckSection } from "@/types/service-setup/obligation-check-section";
import { Criteria, CriteriaOption } from "@/types/service-setup/criteria";
import { Country } from "@/types/country";
import { getCountryByCode } from "@/lib/api/countries";
import { CountryIcon } from "@/components/ui/country-icon";

interface PreviewObligationDialogProps {
  countryCode: string;
  children: React.ReactNode;
}

type PreviewQuestionBranch =
  | { action: "does_not_indicate_obligation" }
  | { action: "indicates_obligation"; services: string[] }
  | { action: "show_conditional"; targetQuestionNumber?: number };

interface PreviewQuestion {
  id: number;
  title: string;
  yes: PreviewQuestionBranch;
  no: PreviewQuestionBranch;
}

interface PreviewSection {
  id: number;
  title: string;
  questions: PreviewQuestion[];
}

export function PreviewObligationDialog({ countryCode, children }: PreviewObligationDialogProps) {
  const { data: country } = useQuery<Country>({
    queryKey: ["country", countryCode],
    queryFn: () => getCountryByCode(countryCode),
  });

  const { data: preview } = useQuery<{ sections: PreviewSection[] }>({
    queryKey: ["preview-obligation", countryCode],
    queryFn: async () => {
      const sectionsPromise = getObligationCheckSectionsByCountryCode(countryCode);
      const servicesPromise = getServiceSetupPackagingServices(countryCode);
      const [sections, services] = await Promise.all([sectionsPromise, servicesPromise]);

      const sectionsWithCriterias: Array<{ section: ObligationCheckSection; criterias: Criteria[] }> =
        await Promise.all(
          (sections || []).map(async (sec): Promise<{ section: ObligationCheckSection; criterias: Criteria[] }> => {
            const criterias = await getCriteriasBySectionId(sec.id);
            const active = (criterias || []).filter((c): c is Criteria => !c.deleted_at);
            return { section: sec, criterias: active };
          })
        );

      const orderedCriteriaIds: number[] = sectionsWithCriterias
        .flatMap((s) => s.criterias)
        .map((c) => c.id)
        .filter((id): id is number => typeof id === "number");

      const idToNumber = new Map<number, number>();
      orderedCriteriaIds.forEach((id, idx) => idToNumber.set(id, idx + 1));

      const serviceIdToName = new Map<number, string>();
      (services || []).forEach((s) => serviceIdToName.set(s.id, s.name));

      const mapBranch = (option: CriteriaOption | undefined): PreviewQuestionBranch => {
        if (!option) return { action: "does_not_indicate_obligation" };
        if (option.value === "CONDITIONAL" && option.conditional_criteria_id) {
          return {
            action: "show_conditional",
            targetQuestionNumber: idToNumber.get(option.conditional_criteria_id),
          };
        }
        if (option.value === "OBLIGED") {
          const servicesNames = (option.packaging_service_ids || [])
            .map((sid) => serviceIdToName.get(sid))
            .filter((n): n is string => !!n);
          return { action: "indicates_obligation", services: servicesNames };
        }
        return { action: "does_not_indicate_obligation" };
      };

      const sectionsTransformed: PreviewSection[] = sectionsWithCriterias.map(({ section, criterias }, idx) => {
        const questions: PreviewQuestion[] = criterias.map((c) => {
          const yesOption = (c.options || []).find((o) => o.option_value === "YES");
          const noOption = (c.options || []).find((o) => o.option_value === "NO");
          return {
            id: c.id,
            title: c.title || `Question ${idToNumber.get(c.id) || ""}`,
            yes: mapBranch(yesOption),
            no: mapBranch(noOption),
          };
        });
        return {
          id: section.id,
          title: section.title || `Section ${idx + 1}`,
          questions,
        };
      });

      return { sections: sectionsTransformed };
    },
    staleTime: 0,
  });

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="bg-surface-02 py-9 max-w-3xl">
        <DialogHeader>
          <DialogTitle>Preview obligation check</DialogTitle>
          <DialogDescription>Check how the questions will be set for the user to answer</DialogDescription>
        </DialogHeader>
        <div className="bg-background rounded-4xl p-8 flex flex-col gap-6">
          {!!country && (
            <div className="flex items-center gap-3 pb-2">
              <CountryIcon className="size-6" country={country} />
              <p className="text-primary text-xl font-bold">{country.name}</p>
            </div>
          )}
          <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
          {!preview?.sections?.length && (
            <div className="py-4 flex items-center gap-2">
              <AlertCircle className="size-5 text-error stroke-white" />
              <p className="text-error text-sm">No questions registered yet!</p>
            </div>
          )}

          {preview?.sections?.map((section, idx) => (
            <div key={section.id} className="space-y-4">
              <div className="flex items-center justify-between">
                <p className="text-primary font-bold">
                  Section {idx + 1} of {preview.sections.length}
                </p>
                {idx > 0 && <div className="h-[1px] flex-1 ml-6 bg-tonal-dark-cream-80 rounded-full" />}
              </div>
              <div className="space-y-6">
                {section.questions.map((q) => (
                  <div key={q.id} className="flex flex-col gap-4">
                    <div className="flex items-start gap-2">
                      <p className="text-base text-tonal-dark-cream-10 flex-1">{q.title}</p>
                    </div>
                    <YesNoPreviewRow />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}

function YesNoPreviewRow() {
  return (
    <div className="space-y-3">
      <PreviewRadio label="Yes" />
      <PreviewRadio label="No" />
    </div>
  );
}

function PreviewRadio({ label }: { label: string }) {
  return (
    <label className="text-sm md:text-base text-tonal-dark-cream-20 flex gap-2 items-center">
      <input type="radio" className="hidden" />
      <span className="w-5 h-5 rounded-full border border-tonal-dark-cream-60" />
      {label}
    </label>
  );
}
