import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { getServiceSetupCommitment } from "@/lib/api/service-setups";
import { RadioSelected, RadioUnselected } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";

interface PreviewCommitmentDialogProps {
  countryCode: string;
  children: React.ReactNode;
}

export function PreviewCommitmentDialog({ countryCode, children }: PreviewCommitmentDialogProps) {
  const { data: criterias } = useQuery({
    queryKey: ["service-setup-commitment", countryCode],
    queryFn: () => getServiceSetupCommitment(countryCode),
    staleTime: 0,
  });

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="bg-surface-02 py-9 max-w-3xl">
        <DialogHeader>
          <DialogTitle>Commitment Assessment</DialogTitle>
          <DialogDescription>Check how the questions will be set for the user to answer</DialogDescription>
        </DialogHeader>
        <div className="bg-background rounded-4xl p-8 flex flex-col gap-5">
          <div className="flex flex-col pb-6">
            <div className="space-y-8 md:space-y-10">
              {criterias?.map((criteria) => (
                <div key={criteria.id} className="flex flex-col gap-4">
                  <p className="text-base text-tonal-dark-cream-10">{criteria.title}</p>
                  {criteria.options.map((option) => (
                    <label
                      key={`${criteria.id}_${option.value}`}
                      className="text-sm md:text-base text-tonal-dark-cream-20 flex gap-2 items-center cursor-pointer has-[input:checked]:cursor-default"
                    >
                      <input
                        name={`countries.${countryCode}.commitment.questions.${`criteria_${criteria.id}`}.answer`}
                        value={option.value}
                        type="radio"
                        className="hidden peer"
                      />
                      <RadioSelected className="hidden peer-checked:block size-5 fill-primary" />
                      <RadioUnselected className="block cursor-pointer peer-checked:hidden size-5 fill-primary peer-data-[invalid=true]:fill-error" />
                      {criteria.input_type === "SELECT" && <span className="mt-1">{option.option_value}</span>}
                      {criteria.input_type === "YES_NO" && <>{option.option_value === "YES" ? "Yes" : "No"}</>}
                    </label>
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
