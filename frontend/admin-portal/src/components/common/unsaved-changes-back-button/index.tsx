"use client";

import { Button } from "@interzero/oneepr-react-ui/Button";
import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { ChevronLeft } from "lucide-react";

interface UnsavedChangesBackButtonProps {
  hasUnsavedChanges: boolean;
  onBack: () => void;
  buttonText?: string;
  dialogTitle?: string;
  dialogDescription?: string;
  leaveButtonText?: string;
}

export function UnsavedChangesBackButton({
  hasUnsavedChanges,
  onBack,
  buttonText = "Back",
  dialogTitle = "Are you sure you want to go back?",
  dialogDescription = "Any unsaved changes will be lost if you leave this page.",
  leaveButtonText = "Leave page",
}: UnsavedChangesBackButtonProps) {
  const [open, setOpen] = useState(false);

  const handleBackClick = () => {
    if (hasUnsavedChanges) {
      setOpen(true);
      return;
    }
    onBack();
  };

  return (
    <>
      <Button
        variant="text"
        color="light-blue"
        size="medium"
        leadingIcon={<ChevronLeft className="size-5" />}
        onClick={handleBackClick}
      >
        {buttonText}
      </Button>

      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{dialogTitle}</AlertDialogTitle>
            <AlertDialogDescription>{dialogDescription}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setOpen(false)}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              color="yellow"
              onClick={() => {
                setOpen(false);
                onBack();
              }}
            >
              {leaveButtonText}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
