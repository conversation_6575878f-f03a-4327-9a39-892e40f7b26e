"use client";

import * as React from "react";
import { cn } from "@/utils/cn";

export interface ChipProps extends React.HTMLAttributes<HTMLSpanElement> {
  as?: "span" | "button";
  size?: "sm" | "md";
  variant?: "beige" | "blue" | "green" | "gray";
  fullWidth?: boolean;
}

export function Chip({
  as = "span",
  size = "md",
  variant = "beige",
  fullWidth,
  className,
  children,
  ...props
}: ChipProps) {
  const Component: React.ElementType = as;

  const sizeClasses = size === "sm" ? "px-3 py-1.5" : "px-4 py-2";

  const variantClasses = {
    beige: "bg-tonal-beige-90 text-primary",
    blue: "bg-tonal-blue-90 text-primary",
    green: "bg-tonal-green-90 text-primary",
    gray: "bg-tonal-dark-cream-96 text-primary",
  }[variant];

  return (
    <Component
      className={cn(
        "inline-flex items-center rounded-xl truncate",
        sizeClasses,
        variantClasses,
        fullWidth ? "w-full" : undefined,
        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
}
